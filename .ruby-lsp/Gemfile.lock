GIT
  remote: https://github.com/basecamp/useragent.git
  revision: 746e37ba93cef6399920d18ad88903db92ba4e30
  specs:
    useragent (0.16.10)

GIT
  remote: https://github.com/rails/rails.git
  revision: 13d5f87208921cc5139cd53b280c42f23c334826
  specs:
    actioncable (8.0.0.beta1)
      actionpack (= 8.0.0.beta1)
      activesupport (= 8.0.0.beta1)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
      zeitwerk (~> 2.6)
    actionmailbox (8.0.0.beta1)
      actionpack (= 8.0.0.beta1)
      activejob (= 8.0.0.beta1)
      activerecord (= 8.0.0.beta1)
      activestorage (= 8.0.0.beta1)
      activesupport (= 8.0.0.beta1)
      mail (>= 2.8.0)
    actionmailer (8.0.0.beta1)
      actionpack (= 8.0.0.beta1)
      actionview (= 8.0.0.beta1)
      activejob (= 8.0.0.beta1)
      activesupport (= 8.0.0.beta1)
      mail (>= 2.8.0)
      rails-dom-testing (~> 2.2)
    actionpack (8.0.0.beta1)
      actionview (= 8.0.0.beta1)
      activesupport (= 8.0.0.beta1)
      nokogiri (>= 1.8.5)
      rack (>= 2.2.4)
      rack-session (>= 1.0.1)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
      useragent (~> 0.16)
    actiontext (8.0.0.beta1)
      actionpack (= 8.0.0.beta1)
      activerecord (= 8.0.0.beta1)
      activestorage (= 8.0.0.beta1)
      activesupport (= 8.0.0.beta1)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (8.0.0.beta1)
      activesupport (= 8.0.0.beta1)
      builder (~> 3.1)
      erubi (~> 1.11)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    activejob (8.0.0.beta1)
      activesupport (= 8.0.0.beta1)
      globalid (>= 0.3.6)
    activemodel (8.0.0.beta1)
      activesupport (= 8.0.0.beta1)
    activerecord (8.0.0.beta1)
      activemodel (= 8.0.0.beta1)
      activesupport (= 8.0.0.beta1)
      timeout (>= 0.4.0)
    activestorage (8.0.0.beta1)
      actionpack (= 8.0.0.beta1)
      activejob (= 8.0.0.beta1)
      activerecord (= 8.0.0.beta1)
      activesupport (= 8.0.0.beta1)
      marcel (~> 1.0)
    activesupport (8.0.0.beta1)
      base64
      benchmark (>= 0.3)
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.3.1)
      connection_pool (>= 2.2.5)
      drb
      i18n (>= 1.6, < 2)
      logger (>= 1.4.2)
      minitest (>= 5.1)
      securerandom (>= 0.3)
      tzinfo (~> 2.0, >= 2.0.5)
      uri (>= 0.13.1)
    rails (8.0.0.beta1)
      actioncable (= 8.0.0.beta1)
      actionmailbox (= 8.0.0.beta1)
      actionmailer (= 8.0.0.beta1)
      actionpack (= 8.0.0.beta1)
      actiontext (= 8.0.0.beta1)
      actionview (= 8.0.0.beta1)
      activejob (= 8.0.0.beta1)
      activemodel (= 8.0.0.beta1)
      activerecord (= 8.0.0.beta1)
      activestorage (= 8.0.0.beta1)
      activesupport (= 8.0.0.beta1)
      bundler (>= 1.15.0)
      railties (= 8.0.0.beta1)
    railties (8.0.0.beta1)
      actionpack (= 8.0.0.beta1)
      activesupport (= 8.0.0.beta1)
      irb (~> 1.13)
      rackup (>= 1.0.0)
      rake (>= 12.2)
      thor (~> 1.0, >= 1.2.2)
      zeitwerk (~> 2.6)

GEM
  remote: https://rubygems.org/
  specs:
    addressable (2.8.6)
      public_suffix (>= 2.0.2, < 6.0)
    ast (2.4.2)
    base64 (0.2.0)
    bcrypt (3.1.20)
    benchmark (0.3.0)
    bigdecimal (3.1.8)
    bindex (0.8.1)
    brakeman (6.2.2)
      racc
    builder (3.3.0)
    capybara (3.40.0)
      addressable
      matrix
      mini_mime (>= 0.1.3)
      nokogiri (~> 1.11)
      rack (>= 1.6.0)
      rack-test (>= 0.6.3)
      regexp_parser (>= 1.5, < 3.0)
      xpath (~> 3.2)
    chunky_png (1.4.0)
    concurrent-ruby (1.3.4)
    connection_pool (2.4.1)
    crass (1.0.6)
    date (3.3.4)
    debug (1.9.2)
      irb (~> 1.10)
      reline (>= 0.3.8)
    drb (2.2.1)
    erubi (1.13.0)
    faker (3.5.1)
      i18n (>= 1.8.11, < 2)
    ffi (1.17.0-aarch64-linux-gnu)
    ffi (1.17.0-arm-linux-gnu)
    ffi (1.17.0-arm64-darwin)
    ffi (1.17.0-x86-linux-gnu)
    ffi (1.17.0-x86_64-darwin)
    ffi (1.17.0-x86_64-linux-gnu)
    front_matter_parser (1.0.1)
    globalid (1.2.1)
      activesupport (>= 6.1)
    i18n (1.14.6)
      concurrent-ruby (~> 1.0)
    image_processing (1.13.0)
      mini_magick (>= 4.9.5, < 5)
      ruby-vips (>= 2.0.17, < 3)
    importmap-rails (2.0.1)
      actionpack (>= 6.0.0)
      activesupport (>= 6.0.0)
      railties (>= 6.0.0)
    io-console (0.7.2)
    irb (1.14.1)
      rdoc (>= 4.0.0)
      reline (>= 0.4.2)
    jbuilder (2.13.0)
      actionview (>= 5.0.0)
      activesupport (>= 5.0.0)
    json (2.7.2)
    language_server-protocol (********)
    logger (1.6.1)
    loofah (2.23.0)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    marcel (1.0.4)
    matrix (0.4.2)
    mini_magick (4.13.2)
    mini_mime (1.1.5)
    minitest (5.25.1)
    mono_logger (1.1.2)
    multi_json (1.15.0)
    mustermann (3.0.0)
      ruby2_keywords (~> 0.0.1)
    net-imap (0.4.16)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-smtp (0.5.0)
      net-protocol
    nio4r (2.7.3)
    nokogiri (1.16.7-aarch64-linux)
      racc (~> 1.4)
    nokogiri (1.16.7-arm-linux)
      racc (~> 1.4)
    nokogiri (1.16.7-arm64-darwin)
      racc (~> 1.4)
    nokogiri (1.16.7-x86-linux)
      racc (~> 1.4)
    nokogiri (1.16.7-x86_64-darwin)
      racc (~> 1.4)
    nokogiri (1.16.7-x86_64-linux)
      racc (~> 1.4)
    parallel (1.25.1)
    parser (3.3.3.0)
      ast (~> 2.4.1)
      racc
    prism (1.4.0)
    propshaft (1.0.0)
      actionpack (>= 7.0.0)
      activesupport (>= 7.0.0)
      rack
      railties (>= 7.0.0)
    psych (5.1.2)
      stringio
    public_suffix (5.0.5)
    puma (6.4.3)
      nio4r (~> 2.0)
    racc (1.8.1)
    rack (3.1.8)
    rack-protection (4.0.0)
      base64 (>= 0.1.0)
      rack (>= 3.0.0, < 4)
    rack-session (2.0.0)
      rack (>= 3.0.0)
    rack-test (2.1.0)
      rack (>= 1.3)
    rackup (2.1.0)
      rack (>= 3)
      webrick (~> 1.8)
    rails-dom-testing (2.2.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.0)
      loofah (~> 2.21)
      nokogiri (~> 1.14)
    rainbow (3.1.1)
    rake (13.2.1)
    rbs (3.9.4)
      logger
    rdoc (6.7.0)
      psych (>= 4.0.0)
    redcarpet (3.6.0)
    redis (5.3.0)
      redis-client (>= 0.22.0)
    redis-client (0.22.2)
      connection_pool
    redis-namespace (1.11.0)
      redis (>= 4)
    regexp_parser (2.9.2)
    reline (0.5.10)
      io-console (~> 0.5)
    resque (2.6.0)
      mono_logger (~> 1.0)
      multi_json (~> 1.0)
      redis-namespace (~> 1.6)
      sinatra (>= 0.9.2)
    resque-pool (0.7.1)
      rake (>= 10.0, < 14.0)
      resque (>= 1.22, < 3)
    rexml (3.3.9)
    rouge (4.5.0)
    rqrcode (2.2.0)
      chunky_png (~> 1.0)
      rqrcode_core (~> 1.0)
    rqrcode_core (1.2.0)
    rubocop (1.64.1)
      json (~> 2.3)
      language_server-protocol (>= 3.17.0)
      parallel (~> 1.10)
      parser (>= 3.3.0.2)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 1.8, < 3.0)
      rexml (>= 3.2.5, < 4.0)
      rubocop-ast (>= 1.31.1, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 2.4.0, < 3.0)
    rubocop-ast (1.31.3)
      parser (>= 3.3.1.0)
    rubocop-minitest (0.35.0)
      rubocop (>= 1.61, < 2.0)
      rubocop-ast (>= 1.31.1, < 2.0)
    rubocop-performance (1.21.0)
      rubocop (>= 1.48.1, < 2.0)
      rubocop-ast (>= 1.31.1, < 2.0)
    rubocop-rails (2.25.0)
      activesupport (>= 4.2.0)
      rack (>= 1.1)
      rubocop (>= 1.33.0, < 2.0)
      rubocop-ast (>= 1.31.1, < 2.0)
    rubocop-rails-omakase (1.0.0)
      rubocop
      rubocop-minitest
      rubocop-performance
      rubocop-rails
    ruby-lsp (0.24.2)
      language_server-protocol (~> 3.17.0)
      prism (>= 1.2, < 2.0)
      rbs (>= 3, < 5)
      sorbet-runtime (>= 0.5.10782)
    ruby-lsp-rails (0.4.6)
      ruby-lsp (>= 0.24.0, < 0.25.0)
    ruby-progressbar (1.13.0)
    ruby-vips (2.2.2)
      ffi (~> 1.12)
      logger
    ruby2_keywords (0.0.5)
    rubyzip (2.3.2)
    securerandom (0.3.1)
    selenium-webdriver (4.26.0)
      base64 (~> 0.2)
      logger (~> 1.4)
      rexml (~> 3.2, >= 3.2.5)
      rubyzip (>= 1.2.2, < 3.0)
      websocket (~> 1.0)
    sinatra (4.0.0)
      mustermann (~> 3.0)
      rack (>= 3.0.0, < 4)
      rack-protection (= 4.0.0)
      rack-session (>= 2.0.0, < 3)
      tilt (~> 2.0)
    sorbet-runtime (0.5.12219)
    sqlite3 (2.2.0-aarch64-linux-gnu)
    sqlite3 (2.2.0-arm-linux-gnu)
    sqlite3 (2.2.0-arm64-darwin)
    sqlite3 (2.2.0-x86-linux-gnu)
    sqlite3 (2.2.0-x86_64-darwin)
    sqlite3 (2.2.0-x86_64-linux-gnu)
    stimulus-rails (1.3.4)
      railties (>= 6.0.0)
    stringio (3.1.1)
    thor (1.3.2)
    thruster (0.1.8)
    thruster (0.1.8-aarch64-linux)
    thruster (0.1.8-arm64-darwin)
    thruster (0.1.8-x86_64-darwin)
    thruster (0.1.8-x86_64-linux)
    tilt (2.3.0)
    timeout (0.4.1)
    turbo-rails (2.0.11)
      actionpack (>= 6.0.0)
      railties (>= 6.0.0)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    unicode-display_width (2.5.0)
    uri (0.13.1)
    web-console (4.2.1)
      actionview (>= 6.0.0)
      activemodel (>= 6.0.0)
      bindex (>= 0.4.0)
      railties (>= 6.0.0)
    webrick (1.8.2)
    websocket (1.2.11)
    websocket-driver (0.7.6)
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    xpath (3.2.0)
      nokogiri (~> 1.8)
    zeitwerk (2.7.1)

PLATFORMS
  aarch64-linux
  arm-linux
  arm64-darwin
  x86-linux
  x86_64-darwin
  x86_64-linux

DEPENDENCIES
  bcrypt (~> 3.1.7)
  brakeman
  capybara
  debug
  faker
  front_matter_parser
  image_processing (~> 1.13)
  importmap-rails
  jbuilder
  propshaft
  puma (>= 5.0)
  rails!
  redcarpet (~> 3.6)
  redis (>= 4.0.1)
  resque (~> 2.6.0)
  resque-pool (~> 0.7.1)
  rouge (~> 4.5)
  rqrcode
  rubocop-rails-omakase
  ruby-lsp
  ruby-lsp-rails
  selenium-webdriver
  sqlite3 (~> 2.2)
  stimulus-rails
  thruster
  turbo-rails
  useragent!
  web-console

RUBY VERSION
   ruby 3.3.5p100

BUNDLED WITH
   2.5.18
