defmodule L3rnDevWeb.MarkdownLive do
  use L3rnDevWeb, :live_view

  @impl true
  def mount(_params, _session, socket) do
    {:ok,
     socket
     |> assign(:content, "")
     |> assign(:selection, %{start: 0, end: 0})
     |> assign(:history, [])
     |> assign(:history_index, -1)
     |> allow_upload(:markdown_files,
       accept: ~w(.jpg .jpeg .png .gif .pdf .txt .md),
       max_entries: 10
     )}
  end

  @impl true
  def handle_event("content_changed", %{"content" => content, "selection" => selection}, socket) do
    socket =
      socket
      |> assign(:content, content)
      |> assign(:selection, selection)
      |> push_history(content)

    {:noreply, socket}
  end

  def handle_event("toolbar_action", %{"action" => action} = _params, socket) do
    handle_toolbar_action(action, socket)
  end

  defp handle_toolbar_action("bold", socket), do: {:noreply, push_event(socket, "toggle_format", %{format: "bold"})}
  defp handle_toolbar_action("italic", socket), do: {:noreply, push_event(socket, "toggle_format", %{format: "italic"})}
  defp handle_toolbar_action("strikethrough", socket), do: {:noreply, push_event(socket, "toggle_format", %{format: "strikethrough"})}
  defp handle_toolbar_action("code", socket), do: {:noreply, push_event(socket, "toggle_format", %{format: "code"})}
  defp handle_toolbar_action("quote", socket), do: {:noreply, push_event(socket, "toggle_format", %{format: "quote"})}
  defp handle_toolbar_action("link", socket), do: {:noreply, push_event(socket, "insert_link", %{})}
  defp handle_toolbar_action("bullet_list", socket), do: {:noreply, push_event(socket, "toggle_format", %{format: "bullet_list"})}
  defp handle_toolbar_action("number_list", socket), do: {:noreply, push_event(socket, "toggle_format", %{format: "number_list"})}
  defp handle_toolbar_action("undo", socket), do: handle_undo(socket)
  defp handle_toolbar_action("redo", socket), do: handle_redo(socket)
  defp handle_toolbar_action(_, socket), do: {:noreply, socket}

  def handle_event("insert_link", %{"text" => text, "url" => url}, socket) do
    {:noreply, push_event(socket, "insert_text", %{text: "[#{text}](#{url})"})}
  end

  def handle_event("validate", _params, socket) do
    {:noreply, socket}
  end

  def handle_event("save", %{"content" => _content}, socket) do
    # Here you would typically save to database
    # For now, we'll just show a success message
    {:noreply, put_flash(socket, :info, "Markdown saved successfully!")}
  end

  def handle_event("upload_progress", %{"ref" => ref, "progress" => progress}, socket) do
    {:noreply, push_event(socket, "upload_progress", %{ref: ref, progress: progress})}
  end

  @impl true
  def handle_progress(:markdown_files, entry, socket) do
    if entry.done? do
      # Process uploaded file
      file_path =
        consume_uploaded_entry(socket, entry, fn %{path: path} ->
          # Copy file to permanent location
          dest =
            Path.join([
              "priv",
              "static",
              "uploads",
              "#{entry.uuid}.#{get_extension(entry.client_name)}"
            ])

          File.cp!(path, dest)
          {:ok, "/uploads/#{entry.uuid}.#{get_extension(entry.client_name)}"}
        end)

      case file_path do
        {:ok, url} ->
          file_name = entry.client_name
          mime_type = entry.client_type

          socket =
            push_event(socket, "file_uploaded", %{
              name: file_name,
              url: url,
              mime_type: mime_type
            })

          {:noreply, socket}

        {:error, _} ->
          {:noreply, put_flash(socket, :error, "Failed to upload file")}
      end
    else
      {:noreply, socket}
    end
  end

  defp handle_undo(socket) do
    history = socket.assigns.history
    current_index = socket.assigns.history_index

    if current_index > 0 do
      new_index = current_index - 1
      content = Enum.at(history, new_index)

      socket =
        socket
        |> assign(:content, content)
        |> assign(:history_index, new_index)
        |> push_event("set_content", %{content: content})

      {:noreply, socket}
    else
      {:noreply, socket}
    end
  end

  defp handle_redo(socket) do
    history = socket.assigns.history
    current_index = socket.assigns.history_index

    if current_index < length(history) - 1 do
      new_index = current_index + 1
      content = Enum.at(history, new_index)

      socket =
        socket
        |> assign(:content, content)
        |> assign(:history_index, new_index)
        |> push_event("set_content", %{content: content})

      {:noreply, socket}
    else
      {:noreply, socket}
    end
  end

  defp push_history(socket, content) do
    history = socket.assigns.history
    current_index = socket.assigns.history_index

    # Don't add to history if content hasn't changed
    if current_index >= 0 and Enum.at(history, current_index) == content do
      socket
    else
      # Truncate history if we're not at the end
      new_history =
        if current_index == length(history) - 1 do
          history ++ [content]
        else
          Enum.take(history, current_index + 1) ++ [content]
        end

      # Keep only last 50 entries
      trimmed_history = Enum.take(new_history, -50)

      socket
      |> assign(:history, trimmed_history)
      |> assign(:history_index, length(trimmed_history) - 1)
    end
  end

  defp get_extension(filename) do
    filename
    |> Path.extname()
    |> String.trim_leading(".")
  end

  defp render_markdown(content) do
    # Simple markdown rendering - in production you'd use a proper markdown parser
    content
    |> String.replace(~r/\*\*(.*?)\*\*/, "<strong>\\1</strong>")
    |> String.replace(~r/\*(.*?)\*/, "<em>\\1</em>")
    |> String.replace(~r/~~(.*?)~~/, "<del>\\1</del>")
    |> String.replace(~r/`(.*?)`/, "<code>\\1</code>")
    |> String.replace(~r/^# (.*$)/m, "<h1>\\1</h1>")
    |> String.replace(~r/^## (.*$)/m, "<h2>\\1</h2>")
    |> String.replace(~r/^### (.*$)/m, "<h3>\\1</h3>")
    |> String.replace(~r/^> (.*$)/m, "<blockquote>\\1</blockquote>")
    |> String.replace(~r/\[([^\]]+)\]\(([^)]+)\)/, "<a href=\"\\2\">\\1</a>")
    |> String.replace(~r/!\[([^\]]*)\]\(([^)]+)\)/, "<img src=\"\\2\" alt=\"\\1\">")
    |> String.replace(~r/\n/, "<br>")
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div class="border border-gray-200 rounded-lg bg-white font-sans" id="markdown-editor" phx-hook="MarkdownEditor">
      <!-- Toolbar -->
      <div class="flex items-center gap-1 p-2 border-b border-gray-200 bg-gray-50 rounded-t-lg">
        <button type="button" phx-click="toolbar_action" phx-value-action="bold" title="Bold" class="flex items-center justify-center w-8 h-8 border-none bg-transparent rounded-md cursor-pointer text-gray-600 transition-all duration-200 ease-in-out hover:bg-gray-200 hover:text-gray-900 active:bg-gray-300">
          <svg viewBox="0 0 24 24" class="w-4 h-4">
            <path d="M6 4h8a4 4 0 0 1 4 4 4 4 0 0 1-4 4H6z" />
            <path d="M6 12h9a4 4 0 0 1 4 4 4 4 0 0 1-4 4H6z" />
          </svg>
        </button>

        <button type="button" phx-click="toolbar_action" phx-value-action="italic" title="Italic">
          <svg viewBox="0 0 24 24" class="w-4 h-4">
            <line x1="19" y1="4" x2="10" y2="4" />
            <line x1="14" y1="20" x2="5" y2="20" />
            <line x1="15" y1="4" x2="9" y2="20" />
          </svg>
        </button>

        <button
          type="button"
          phx-click="toolbar_action"
          phx-value-action="strikethrough"
          title="Strikethrough"
        >
          <svg viewBox="0 0 24 24" class="w-4 h-4">
            <path d="M16 4H9a3 3 0 0 0-2.83 4" />
            <path d="M14 12a4 4 0 0 1 0 8H6" />
            <line x1="4" y1="12" x2="20" y2="12" />
          </svg>
        </button>

        <div class="w-px h-6 bg-gray-200 mx-1"></div>

        <button type="button" phx-click="toolbar_action" phx-value-action="code" title="Code">
          <svg viewBox="0 0 24 24" class="w-4 h-4">
            <polyline points="16,18 22,12 16,6" />
            <polyline points="8,6 2,12 8,18" />
          </svg>
        </button>

        <button type="button" phx-click="toolbar_action" phx-value-action="quote" title="Quote">
          <svg viewBox="0 0 24 24" class="w-4 h-4">
            <path d="M3 21c3 0 7-1 7-8V5c0-1.25-.756-2.017-2-2H4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2 1 0 1 0 1 1v1c0 1-1 2-2 2s-1 .008-1 1.031V20c0 1 0 1 1 1z" />
            <path d="M15 21c3 0 7-1 7-8V5c0-1.25-.757-2.017-2-2h-4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2h.75c0 2.25.25 4-2.75 4v3c0 1 0 1 1 1z" />
          </svg>
        </button>

        <button type="button" phx-click="toolbar_action" phx-value-action="link" title="Link">
          <svg viewBox="0 0 24 24" class="w-4 h-4">
            <path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71" />
            <path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71" />
          </svg>
        </button>

        <div class="w-px h-6 bg-gray-200 mx-1"></div>

        <button
          type="button"
          phx-click="toolbar_action"
          phx-value-action="bullet_list"
          title="Bullet List"
        >
          <svg viewBox="0 0 24 24" class="w-4 h-4">
            <line x1="8" y1="6" x2="21" y2="6" />
            <line x1="8" y1="12" x2="21" y2="12" />
            <line x1="8" y1="18" x2="21" y2="18" />
            <line x1="3" y1="6" x2="3.01" y2="6" />
            <line x1="3" y1="12" x2="3.01" y2="12" />
            <line x1="3" y1="18" x2="3.01" y2="18" />
          </svg>
        </button>

        <button
          type="button"
          phx-click="toolbar_action"
          phx-value-action="number_list"
          title="Numbered List"
        >
          <svg viewBox="0 0 24 24" class="w-4 h-4">
            <line x1="10" y1="6" x2="21" y2="6" />
            <line x1="10" y1="12" x2="21" y2="12" />
            <line x1="10" y1="18" x2="21" y2="18" />
            <path d="M4 6h1v4" />
            <path d="M4 10h2" />
            <path d="M6 18H4c0-1 2-2 2-3s-1-1.5-2-1" />
          </svg>
        </button>

        <div class="w-px h-6 bg-gray-200 mx-1"></div>

        <button type="button" phx-click="toolbar_action" phx-value-action="undo" title="Undo">
          <svg viewBox="0 0 24 24" class="w-4 h-4">
            <path d="M3 7v6h6" />
            <path d="M21 17a9 9 0 0 0-9-9 9 9 0 0 0-6 2.3L3 13" />
          </svg>
        </button>

        <button type="button" phx-click="toolbar_action" phx-value-action="redo" title="Redo">
          <svg viewBox="0 0 24 24" class="w-4 h-4">
            <path d="M21 7v6h-6" />
            <path d="M3 17a9 9 0 0 1 9-9 9 9 0 0 1 6 2.3L21 13" />
          </svg>
        </button>

        <div class="w-px h-6 bg-gray-200 mx-1"></div>

        <label class="file-upload-btn" title="Upload File">
          <svg viewBox="0 0 24 24" class="w-4 h-4">
            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" />
            <polyline points="14,2 14,8 20,8" />
            <line x1="16" y1="13" x2="8" y2="13" />
            <line x1="16" y1="17" x2="8" y2="17" />
            <polyline points="10,9 9,9 8,9" />
          </svg>
          <.live_file_input upload={@uploads.markdown_files} class="hidden" />
        </label>
      </div>
      
    <!-- Editor Container -->
      <div class="editor-container">
        <!-- Editor -->
        <div class="editor-pane">
          <textarea
            id="editor-textarea"
            class="editor-textarea"
            placeholder="Start writing your markdown..."
            phx-hook="EditorTextarea"
            phx-debounce="300"
          ><%= @content %></textarea>
        </div>
        
    <!-- Preview -->
        <div class="preview-pane">
          <div class="preview-content">
            {raw(render_markdown(@content))}
          </div>
        </div>
      </div>
      
    <!-- Upload Progress -->
      <div
        class="upload-progress"
        style={if @uploads.markdown_files.entries == [], do: "display: none"}
      >
        <%= for entry <- @uploads.markdown_files.entries do %>
          <div class="upload-item" data-ref={entry.ref}>
            <div class="upload-info">
              <span class="filename">{entry.client_name}</span>
              <span class="filesize">{format_bytes(entry.client_size)}</span>
            </div>
            <div class="progress-bar">
              <div class="progress-fill" style={"width: #{entry.progress}%"}></div>
            </div>
            <div class="upload-status">
              <%= if entry.done? do %>
                <span class="status-done">✓</span>
              <% else %>
                <span class="status-progress">{entry.progress}%</span>
              <% end %>
            </div>
          </div>
        <% end %>
      </div>
      
    <!-- Form for saving -->
      <form phx-submit="save" class="save-form">
        <input type="hidden" name="content" value={@content} />
        <button type="submit" class="save-btn">Save Markdown</button>
      </form>
    </div>

    

    <script>
      // Collocated Hook for the main markdown editor
      window.MarkdownEditor = {
        mounted() {
          this.textarea = this.el.querySelector('#editor-textarea');
          this.setupEventListeners();
        },

        setupEventListeners() {
          // Handle content changes
          this.textarea.addEventListener('input', (e) => {
            this.pushEvent('content_changed', {
              content: e.target.value,
              selection: {
                start: e.target.selectionStart,
                end: e.target.selectionEnd
              }
            });
          });

          // Handle keyboard shortcuts
          this.textarea.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
              switch(e.key) {
                case 'b':
                  e.preventDefault();
                  this.pushEvent('toolbar_action', {action: 'bold'});
                  break;
                case 'i':
                  e.preventDefault();
                  this.pushEvent('toolbar_action', {action: 'italic'});
                  break;
                case 'z':
                  e.preventDefault();
                  if (e.shiftKey) {
                    this.pushEvent('toolbar_action', {action: 'redo'});
                  } else {
                    this.pushEvent('toolbar_action', {action: 'undo'});
                  }
                  break;
              }
            }
          });

          // Handle drag and drop
          this.textarea.addEventListener('dragover', (e) => {
            e.preventDefault();
            this.textarea.classList.add('drag-over');
          });

          this.textarea.addEventListener('dragleave', (e) => {
            this.textarea.classList.remove('drag-over');
          });

          this.textarea.addEventListener('drop', (e) => {
            e.preventDefault();
            this.textarea.classList.remove('drag-over');

            const files = Array.from(e.dataTransfer.files);
            if (files.length > 0) {
              // Handle file upload through LiveView uploads
              const input = this.el.querySelector('input[type="file"]');
              input.files = e.dataTransfer.files;
              input.dispatchEvent(new Event('change', { bubbles: true }));
            }
          });
        },

        // Handle server events
        handleEvent(event, payload) {
          switch(event) {
            case 'toggle_format':
              this.toggleFormat(payload.format);
              break;
            case 'insert_link':
              this.insertLink();
              break;
            case 'insert_text':
              this.insertText(payload.text);
              break;
            case 'set_content':
              this.setContent(payload.content);
              break;
            case 'file_uploaded':
              this.insertFile(payload);
              break;
          }
        },

        toggleFormat(format) {
          const start = this.textarea.selectionStart;
          const end = this.textarea.selectionEnd;
          const selectedText = this.textarea.value.substring(start, end);

          let newText = '';

          switch(format) {
            case 'bold':
              newText = `**${selectedText}**`;
              break;
            case 'italic':
              newText = `*${selectedText}*`;
              break;
            case 'strikethrough':
              newText = `~~${selectedText}~~`;
              break;
            case 'code':
              newText = selectedText.includes('\n') ? `\n\`\`\`\n${selectedText}\n\`\`\`\n` : `\`${selectedText}\``;
              break;
            case 'quote':
              newText = `> ${selectedText}`;
              break;
            case 'bullet_list':
              newText = `- ${selectedText}`;
              break;
            case 'number_list':
              newText = `1. ${selectedText}`;
              break;
          }

          this.replaceSelection(newText);
        },

        insertLink() {
          const start = this.textarea.selectionStart;
          const end = this.textarea.selectionEnd;
          const selectedText = this.textarea.value.substring(start, end);

          const linkText = selectedText || 'link text';
          const linkUrl = prompt('Enter URL:', 'https://');

          if (linkUrl) {
            this.replaceSelection(`[${linkText}](${linkUrl})`);
          }
        },

        insertText(text) {
          this.replaceSelection(text);
        },

        setContent(content) {
          this.textarea.value = content;
          this.textarea.focus();
        },

        insertFile(file) {
          const text = file.mime_type.startsWith('image/')
            ? `![${file.name}](${file.url})`
            : `[${file.name}](${file.url})`;

          this.insertText(text);
        },

        replaceSelection(newText) {
          const start = this.textarea.selectionStart;
          const end = this.textarea.selectionEnd;

          this.textarea.setRangeText(newText, start, end, 'end');
          this.textarea.focus();

          // Trigger input event to update LiveView
          this.textarea.dispatchEvent(new Event('input', { bubbles: true }));
        }
      };

      // Collocated Hook for the textarea
      window.EditorTextarea = {
        mounted() {
          this.el.focus();
        },

        updated() {
          // Preserve cursor position if needed
        }
      };
    </script>
    """
  end

  defp format_bytes(bytes) when bytes < 1024, do: "#{bytes} B"
  defp format_bytes(bytes) when bytes < 1024 * 1024, do: "#{Float.round(bytes / 1024, 1)} KB"
  defp format_bytes(bytes), do: "#{Float.round(bytes / (1024 * 1024), 1)} MB"
end
