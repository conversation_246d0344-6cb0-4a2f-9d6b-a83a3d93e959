defmodule L3rnDevWeb.MarkdownLive do
  use L3rnDevWeb, :live_view

  @impl true
  def mount(_params, _session, socket) do
    {:ok,
     socket
     |> assign(:content, "")
     |> assign(:selection, %{start: 0, end: 0})
     |> assign(:history, [])
     |> assign(:history_index, -1)
     |> allow_upload(:markdown_files,
       accept: ~w(.jpg .jpeg .png .gif .pdf .txt .md),
       max_entries: 10
     )}
  end

  @impl true
  def handle_event("content_changed", %{"content" => content, "selection" => selection}, socket) do
    socket =
      socket
      |> assign(:content, content)
      |> assign(:selection, selection)
      |> push_history(content)

    {:noreply, socket}
  end

  def handle_event("toolbar_action", %{"action" => action} = _params, socket) do
    handle_toolbar_action(action, socket)
  end

  defp handle_toolbar_action("bold", socket), do: {:noreply, push_event(socket, "toggle_format", %{format: "bold"})}
  defp handle_toolbar_action("italic", socket), do: {:noreply, push_event(socket, "toggle_format", %{format: "italic"})}
  defp handle_toolbar_action("strikethrough", socket), do: {:noreply, push_event(socket, "toggle_format", %{format: "strikethrough"})}
  defp handle_toolbar_action("code", socket), do: {:noreply, push_event(socket, "toggle_format", %{format: "code"})}
  defp handle_toolbar_action("quote", socket), do: {:noreply, push_event(socket, "toggle_format", %{format: "quote"})}
  defp handle_toolbar_action("link", socket), do: {:noreply, push_event(socket, "insert_link", %{})}
  defp handle_toolbar_action("bullet_list", socket), do: {:noreply, push_event(socket, "toggle_format", %{format: "bullet_list"})}
  defp handle_toolbar_action("number_list", socket), do: {:noreply, push_event(socket, "toggle_format", %{format: "number_list"})}
  defp handle_toolbar_action("undo", socket), do: handle_undo(socket)
  defp handle_toolbar_action("redo", socket), do: handle_redo(socket)
  defp handle_toolbar_action(_, socket), do: {:noreply, socket}

  def handle_event("insert_link", %{"text" => text, "url" => url}, socket) do
    {:noreply, push_event(socket, "insert_text", %{text: "[#{text}](#{url})"})}
  end

  def handle_event("validate", _params, socket) do
    {:noreply, socket}
  end

  def handle_event("save", %{"content" => _content}, socket) do
    # Here you would typically save to database
    # For now, we'll just show a success message
    {:noreply, put_flash(socket, :info, "Markdown saved successfully!")}
  end

  def handle_event("upload_progress", %{"ref" => ref, "progress" => progress}, socket) do
    {:noreply, push_event(socket, "upload_progress", %{ref: ref, progress: progress})}
  end

  @impl true
  def handle_progress(:markdown_files, entry, socket) do
    if entry.done? do
      # Process uploaded file
      file_path =
        consume_uploaded_entry(socket, entry, fn %{path: path} ->
          # Copy file to permanent location
          dest =
            Path.join([
              "priv",
              "static",
              "uploads",
              "#{entry.uuid}.#{get_extension(entry.client_name)}"
            ])

          File.cp!(path, dest)
          {:ok, "/uploads/#{entry.uuid}.#{get_extension(entry.client_name)}"}
        end)

      case file_path do
        {:ok, url} ->
          file_name = entry.client_name
          mime_type = entry.client_type

          socket =
            push_event(socket, "file_uploaded", %{
              name: file_name,
              url: url,
              mime_type: mime_type
            })

          {:noreply, socket}

        {:error, _} ->
          {:noreply, put_flash(socket, :error, "Failed to upload file")}
      end
    else
      {:noreply, socket}
    end
  end

  defp handle_undo(socket) do
    history = socket.assigns.history
    current_index = socket.assigns.history_index

    if current_index > 0 do
      new_index = current_index - 1
      content = Enum.at(history, new_index)

      socket =
        socket
        |> assign(:content, content)
        |> assign(:history_index, new_index)
        |> push_event("set_content", %{content: content})

      {:noreply, socket}
    else
      {:noreply, socket}
    end
  end

  defp handle_redo(socket) do
    history = socket.assigns.history
    current_index = socket.assigns.history_index

    if current_index < length(history) - 1 do
      new_index = current_index + 1
      content = Enum.at(history, new_index)

      socket =
        socket
        |> assign(:content, content)
        |> assign(:history_index, new_index)
        |> push_event("set_content", %{content: content})

      {:noreply, socket}
    else
      {:noreply, socket}
    end
  end

  defp push_history(socket, content) do
    history = socket.assigns.history
    current_index = socket.assigns.history_index

    # Don't add to history if content hasn't changed
    if current_index >= 0 and Enum.at(history, current_index) == content do
      socket
    else
      # Truncate history if we're not at the end
      new_history =
        if current_index == length(history) - 1 do
          history ++ [content]
        else
          Enum.take(history, current_index + 1) ++ [content]
        end

      # Keep only last 50 entries
      trimmed_history = Enum.take(new_history, -50)

      socket
      |> assign(:history, trimmed_history)
      |> assign(:history_index, length(trimmed_history) - 1)
    end
  end

  defp get_extension(filename) do
    filename
    |> Path.extname()
    |> String.trim_leading(".")
  end

  defp render_markdown(content) do
    # Simple markdown rendering - in production you'd use a proper markdown parser
    content
    |> String.replace(~r/\*\*(.*?)\*\*/, "<strong>\\1</strong>")
    |> String.replace(~r/\*(.*?)\*/, "<em>\\1</em>")
    |> String.replace(~r/~~(.*?)~~/, "<del>\\1</del>")
    |> String.replace(~r/`(.*?)`/, "<code>\\1</code>")
    |> String.replace(~r/^# (.*$)/m, "<h1>\\1</h1>")
    |> String.replace(~r/^## (.*$)/m, "<h2>\\1</h2>")
    |> String.replace(~r/^### (.*$)/m, "<h3>\\1</h3>")
    |> String.replace(~r/^> (.*$)/m, "<blockquote>\\1</blockquote>")
    |> String.replace(~r/\[([^\]]+)\]\(([^)]+)\)/, "<a href=\"\\2\">\\1</a>")
    |> String.replace(~r/!\[([^\]]*)\]\(([^)]+)\)/, "<img src=\"\\2\" alt=\"\\1\">")
    |> String.replace(~r/\n/, "<br>")
  end

  @impl true
  def render(assigns) do
    ~H"""
    <house-md
      class="flex flex-col flex-grow font-sans"
      id="markdown-editor"
      phx-hook="HouseMdEditor"
      data-uploads-url="/uploads"
    >
      <!-- House MD Toolbar -->
      <house-md-toolbar class="inline-flex gap-0.5 rounded-lg">
        <button
          data-house-md-action="bold"
          title="Bold"
          class="flex items-center justify-center w-8 h-8 border-none bg-transparent rounded-md cursor-pointer text-gray-600 transition-all duration-200 ease-in-out hover:bg-gray-200 hover:text-gray-900 active:bg-gray-300 hover:scale-105"
        >
          <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 fill-current">
            <path d="m4.1 23c-.5 0-.7-.4-.7-.7v-20.6c0-.4.4-.7.7-.7h8.9c2 0 3.8.6 4.9 1.5 1.2 1 1.8 2.4 1.8 4.1s-.9 3.2-2.3 4.1c-.2 0-.3.3-.3.5s0 .4.3.5c1.9.8 3.2 2.7 3.2 5s-.7 3.6-2.1 4.7-3.3 1.7-5.6 1.7h-8.8zm4.2-18.1v5.1h3c1.2 0 2-.3 2.7-.7.6-.5.9-1.1.9-1.9s-.3-1.4-.8-1.8-1.3-.6-2.3-.6-2.4 0-3.5 0zm0 8.5v5.8h3.7c1.3 0 2.2-.3 2.8-.7s.9-1.2.9-2.2-.4-1.7-1-2.1-1.7-.7-2.9-.7-2.4 0-3.5 0z" fill-rule="evenodd"/>
          </svg>
        </button>

        <button
          data-house-md-action="italic"
          title="Italic"
          class="flex items-center justify-center w-8 h-8 border-none bg-transparent rounded-md cursor-pointer text-gray-600 transition-all duration-200 ease-in-out hover:bg-gray-200 hover:text-gray-900 active:bg-gray-300 hover:scale-105"
        >
          <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 fill-current">
            <path d="m9.3 1h10.2v3.1h-3.5l-3.7 15.7h3.2v3.2h-11v-3.1h3.8l3.7-15.7h-2.8v-3.2z" fill-rule="evenodd"/>
          </svg>
        </button>

        <button
          data-house-md-action="quote"
          title="Quote"
          class="flex items-center justify-center w-8 h-8 border-none bg-transparent rounded-md cursor-pointer text-gray-600 transition-all duration-200 ease-in-out hover:bg-gray-200 hover:text-gray-900 active:bg-gray-300 hover:scale-105"
        >
          <svg viewBox="0 0 24 22" xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 fill-current">
            <path d="m1.1 5.2c.6-.7 1.4-1.3 2.4-1.4 2.6-.4 4.2.4 5.3 1.9 2 2.3 1.9 5.1.6 7.6-1.3 2.4-4 4.6-7.2 5.1-.4 0-.7-.1-1-.4-.1-.3-.1-.7.3-1.1l1.1-1.1c.3-.4.6-.7.7-1.1s.3-.9 0-1.3c0-.4-.6-.7-1-1-1.2-.8-2.3-2.2-2.3-4.1.1-1.4.4-2.4 1.1-3.1z"/>
            <path d="m14.6 5.2c.6-.7 1.6-1.1 2.6-1.4 2.4-.4 4.2.4 5.3 1.9 2 2.3 1.9 5.1.6 7.6-1.3 2.4-4 4.6-7.2 5.1-.4 0-.7-.1-1-.4-.1-.3-.1-.7.3-1.1l1.1-1.1c.3-.4.6-.7.7-1.1s.3-.9 0-1.3c-.1-.4-.6-.7-1-1-1.3-.6-2.4-2-2.4-3.9s.4-2.6 1-3.3z"/>
          </svg>
        </button>

        <button
          data-house-md-action="code"
          title="Code"
          class="flex items-center justify-center w-8 h-8 border-none bg-transparent rounded-md cursor-pointer text-gray-600 transition-all duration-200 ease-in-out hover:bg-gray-200 hover:text-gray-900 active:bg-gray-300 hover:scale-105"
        >
          <svg viewBox="0 0 24 22" xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 fill-current">
            <path d="m.4 10.1c-.5.5-.5 1.4 0 1.9l5.3 5.3c.5.5 1.4.5 1.9 0s.5-1.4 0-1.9l-4.4-4.4 4.4-4.4c.5-.5.5-1.4 0-1.9s-1.3-.5-1.9 0c0 0-5.3 5.4-5.3 5.4zm17.9 7.2 5.3-5.3c.5-.5.5-1.4 0-1.9l-5.3-5.3c-.5-.5-1.4-.5-1.9 0s-.5 1.4 0 1.9l4.4 4.4-4.4 4.4c-.5.5-.5 1.4 0 1.9.5.4 1.4.4 1.9-.1z" fill-rule="evenodd"/>
          </svg>
        </button>

        <button
          data-house-md-action="link"
          title="Link"
          class="flex items-center justify-center w-8 h-8 border-none bg-transparent rounded-md cursor-pointer text-gray-600 transition-all duration-200 ease-in-out hover:bg-gray-200 hover:text-gray-900 active:bg-gray-300 hover:scale-105"
        >
          <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 fill-current">
            <path d="m9.4 14.5c-2.3-2.3-2.3-5.9 0-8.2l4.6-4.6c1.1-1 2.6-1.7 4.2-1.7s3 .5 4.1 1.7c2.3 2.3 2.3 5.9 0 8.2l-2.7 2.3c-.5.5-1.2.5-1.8 0-.5-.5-.5-1.2 0-1.7l2.7-2.3c1.4-1.3 1.4-3.4 0-4.7-.7-.7-1.5-.9-2.3-.9s-1.8.4-2.5.9l-4.7 4.5c-1.4 1.3-1.4 3.4 0 4.7.5.5.5 1.2 0 1.7-.1.3-.4.4-.8.4s-.5-.1-.8-.3z"/>
            <path d="m1.7 22.3c-2.3-2.3-2.3-5.9 0-8.2l2.6-2.5c.5-.5 1.2-.5 1.8 0 .5.5.5 1.2 0 1.7l-2.6 2.5c-1.4 1.3-1.4 3.4 0 4.7.7.7 1.5.9 2.3.9s1.8-.4 2.3-.9l4.6-4.6c1.4-1.3 1.4-3.4 0-4.7-.5-.4-.5-1.2 0-1.7s1.2-.5 1.8 0c2.3 2.3 2.3 5.9 0 8.2l-4.6 4.6c-1 1-2.5 1.7-4.1 1.7s-3-.7-4.1-1.7z"/>
          </svg>
        </button>

        <button
          data-house-md-action="bulletList"
          title="Bullet list"
          class="flex items-center justify-center w-8 h-8 border-none bg-transparent rounded-md cursor-pointer text-gray-600 transition-all duration-200 ease-in-out hover:bg-gray-200 hover:text-gray-900 active:bg-gray-300 hover:scale-105"
        >
          <svg viewBox="0 0 24 22" xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 fill-current">
            <path d="m2.1 4.8c1.1 0 2.1-.9 2.1-2.1s-1-2-2.1-2-2.1.9-2.1 2.1.9 2 2.1 2zm4.1-2c0-.8.6-1.4 1.4-1.4h15.1c.7 0 1.3.6 1.3 1.4s-.6 1.4-1.4 1.4h-15.1c-.7 0-1.3-.7-1.3-1.4zm1.3 6.8c-.8 0-1.4.6-1.4 1.4s.6 1.4 1.4 1.4h15.1c.8 0 1.4-.6 1.4-1.4s-.6-1.4-1.4-1.4zm0 8.3c-.8 0-1.4.6-1.4 1.4s.6 1.4 1.4 1.4h15.1c.8 0 1.4-.6 1.4-1.4s-.6-1.4-1.4-1.4zm-3.4-6.9c0 1.1-.9 2.1-2.1 2.1s-2-1-2-2.1.9-2.1 2.1-2.1 2 1 2 2.1zm-2 10.3c1.1 0 2.1-.9 2.1-2.1s-.9-2.1-2.1-2.1-2.1 1-2.1 2.1.9 2.1 2.1 2.1z" fill-rule="evenodd"/>
          </svg>
        </button>

        <button
          data-house-md-action="numberList"
          title="Numbered list"
          class="flex items-center justify-center w-8 h-8 border-none bg-transparent rounded-md cursor-pointer text-gray-600 transition-all duration-200 ease-in-out hover:bg-gray-200 hover:text-gray-900 active:bg-gray-300 hover:scale-105"
        >
          <svg viewBox="0 0 24 22" xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 fill-current">
            <path d="m0 .3h2.7v5.3h-1.4v-4h-1.3zm6.7 2.7c0-.7.6-1.3 1.3-1.3h14.7c.7 0 1.3.6 1.3 1.3s-.6 1.3-1.3 1.3h-14.7c-.7 0-1.3-.6-1.3-1.3zm1.3 6.7c-.7 0-1.3.6-1.3 1.3s.6 1.3 1.3 1.3h14.7c.7 0 1.3-.6 1.3-1.3s-.6-1.3-1.3-1.3zm0 8c-.7 0-1.3.6-1.3 1.3s.6 1.3 1.3 1.3h14.7c.7 0 1.3-.6 1.3-1.3s-.6-1.3-1.3-1.3zm-4.7-9.4h.7v1.3l-2 2.7h2v1.3h-4v-1.3l2.2-2.7h-2.2v-1.3zm-3.3 9.4v-1.3h4v5.3h-4v-1.3h2.7v-.7h-1.4v-1.3h1.3v-.7z" fill-rule="evenodd"/>
          </svg>
        </button>

        <label
          title="Add Image"
          class="flex items-center justify-center w-8 h-8 border-none bg-transparent rounded-md cursor-pointer text-gray-600 transition-all duration-200 ease-in-out hover:bg-gray-200 hover:text-gray-900 active:bg-gray-300 hover:scale-105"
        >
          <svg viewBox="0 0 24 20" xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 fill-current">
            <path d="m22 20h-20c-1.1 0-2-.9-2-2.1v-15.8c0-1.2.9-2.1 2-2.1h20c1.1 0 2 .9 2 2.1v15.8c0 1.1-.9 2.1-2 2.1zm0-2.9v-14.5c0-.3-.2-.5-.5-.5h-19c-.3 0-.5.2-.5.5v14.5c0 .1.1.2.2.2s.2 0 .2-.1l2.2-3.3c.1-.2.3-.3.5-.3h.7l2.6-4c.1-.2.3-.3.5-.3h.7c.2 0 .4.1.5.3l5.3 8c0 .1.2.2.3.2h.3c.2 0 .4-.2.4-.4s0-.2 0-.2l-1.3-1.9c-.2-.2-.2-.6 0-.8l1.2-1.6c.1-.2.3-.3.5-.3h1.1c.2 0 .4 0 .5.3l3.2 4.4c0 .1.3.2.4 0 .2 0 .2 0 .2-.2zm-5.5-7.6c-1.4 0-2.5-1.2-2.5-2.6s1.1-2.6 2.5-2.6 2.5 1.2 2.5 2.6-1.1 2.6-2.5 2.6z" fill-rule="evenodd"/>
          </svg>
          <.live_file_input upload={@uploads.markdown_files} data-house-md-toolbar-file-picker="true" class="hidden" />
        </label>
      </house-md-toolbar>

      <!-- House MD Content Area -->
      <div
        class="house-md-content flex-grow min-h-[50vh] text-left whitespace-pre-wrap caret-blue-500 focus:outline-none focus:border-none active:outline-none active:border-none p-4 bg-white"
        contenteditable="true"
        id="house-md-content"
        phx-hook="HouseMdContent"
        data-placeholder="Start writing your markdown..."
      ><%= @content %></div>

      <!-- Upload Progress -->
      <%= for entry <- @uploads.markdown_files.entries do %>
        <house-md-upload
          class="rounded-lg my-2 relative"
          data-ref={entry.ref}
          status={cond do
            entry.done? -> "complete"
            entry.cancelled? -> "failed"
            true -> "uploading"
          end}
        >
          <%= if entry.cancelled? do %>
            <button
              class="md-close absolute top-2 right-2 w-4 h-4 bg-transparent border-none cursor-pointer outline-none text-white font-bold text-2xl flex items-center justify-center"
              phx-click="cancel_upload"
              phx-value-ref={entry.ref}
              aria-label="Close"
            >
              ×
            </button>
            <div class="bg-red-500 text-white font-bold p-2 rounded-lg">
              Upload failed: {entry.client_name}
            </div>
          <% else %>
            <div class="md-file font-normal text-green-500">
              {entry.client_name}
            </div>
            <progress
              class="md-progress-bar w-full h-2 bg-gray-200 rounded-lg appearance-none block leading-inherit m-0"
              max="100"
              value={entry.progress}
              style={if entry.done?, do: "display: none", else: "display: block"}
            >
              {entry.progress}%
            </progress>
          <% end %>
        </house-md-upload>
      <% end %>

      <!-- Form for saving -->
      <form phx-submit="save" class="mt-4 p-4 border-t border-gray-200">
        <input type="hidden" name="content" value={@content} />
        <button
          type="submit"
          class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors duration-200"
        >
          Save Markdown
        </button>
      </form>
    </house-md>



    <script>
      // Collocated Hook for the main markdown editor
      window.MarkdownEditor = {
        mounted() {
          this.textarea = this.el.querySelector('#editor-textarea');
          this.setupEventListeners();
        },

        setupEventListeners() {
          // Handle content changes
          this.textarea.addEventListener('input', (e) => {
            this.pushEvent('content_changed', {
              content: e.target.value,
              selection: {
                start: e.target.selectionStart,
                end: e.target.selectionEnd
              }
            });
          });

          // Handle keyboard shortcuts
          this.textarea.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
              switch(e.key) {
                case 'b':
                  e.preventDefault();
                  this.pushEvent('toolbar_action', {action: 'bold'});
                  break;
                case 'i':
                  e.preventDefault();
                  this.pushEvent('toolbar_action', {action: 'italic'});
                  break;
                case 'z':
                  e.preventDefault();
                  if (e.shiftKey) {
                    this.pushEvent('toolbar_action', {action: 'redo'});
                  } else {
                    this.pushEvent('toolbar_action', {action: 'undo'});
                  }
                  break;
              }
            }
          });

          // Handle drag and drop
          this.textarea.addEventListener('dragover', (e) => {
            e.preventDefault();
            this.textarea.classList.add('drag-over');
          });

          this.textarea.addEventListener('dragleave', (e) => {
            this.textarea.classList.remove('drag-over');
          });

          this.textarea.addEventListener('drop', (e) => {
            e.preventDefault();
            this.textarea.classList.remove('drag-over');

            const files = Array.from(e.dataTransfer.files);
            if (files.length > 0) {
              // Handle file upload through LiveView uploads
              const input = this.el.querySelector('input[type="file"]');
              input.files = e.dataTransfer.files;
              input.dispatchEvent(new Event('change', { bubbles: true }));
            }
          });
        },

        // Handle server events
        handleEvent(event, payload) {
          switch(event) {
            case 'toggle_format':
              this.toggleFormat(payload.format);
              break;
            case 'insert_link':
              this.insertLink();
              break;
            case 'insert_text':
              this.insertText(payload.text);
              break;
            case 'set_content':
              this.setContent(payload.content);
              break;
            case 'file_uploaded':
              this.insertFile(payload);
              break;
          }
        },

        toggleFormat(format) {
          const start = this.textarea.selectionStart;
          const end = this.textarea.selectionEnd;
          const selectedText = this.textarea.value.substring(start, end);

          let newText = '';

          switch(format) {
            case 'bold':
              newText = `**${selectedText}**`;
              break;
            case 'italic':
              newText = `*${selectedText}*`;
              break;
            case 'strikethrough':
              newText = `~~${selectedText}~~`;
              break;
            case 'code':
              newText = selectedText.includes('\n') ? `\n\`\`\`\n${selectedText}\n\`\`\`\n` : `\`${selectedText}\``;
              break;
            case 'quote':
              newText = `> ${selectedText}`;
              break;
            case 'bullet_list':
              newText = `- ${selectedText}`;
              break;
            case 'number_list':
              newText = `1. ${selectedText}`;
              break;
          }

          this.replaceSelection(newText);
        },

        insertLink() {
          const start = this.textarea.selectionStart;
          const end = this.textarea.selectionEnd;
          const selectedText = this.textarea.value.substring(start, end);

          const linkText = selectedText || 'link text';
          const linkUrl = prompt('Enter URL:', 'https://');

          if (linkUrl) {
            this.replaceSelection(`[${linkText}](${linkUrl})`);
          }
        },

        insertText(text) {
          this.replaceSelection(text);
        },

        setContent(content) {
          this.textarea.value = content;
          this.textarea.focus();
        },

        insertFile(file) {
          const text = file.mime_type.startsWith('image/')
            ? `![${file.name}](${file.url})`
            : `[${file.name}](${file.url})`;

          this.insertText(text);
        },

        replaceSelection(newText) {
          const start = this.textarea.selectionStart;
          const end = this.textarea.selectionEnd;

          this.textarea.setRangeText(newText, start, end, 'end');
          this.textarea.focus();

          // Trigger input event to update LiveView
          this.textarea.dispatchEvent(new Event('input', { bubbles: true }));
        }
      };

      // Collocated Hook for the textarea
      window.EditorTextarea = {
        mounted() {
          this.el.focus();
        },

        updated() {
          // Preserve cursor position if needed
        }
      };
    </script>
    """
  end

  defp format_bytes(bytes) when bytes < 1024, do: "#{bytes} B"
  defp format_bytes(bytes) when bytes < 1024 * 1024, do: "#{Float.round(bytes / 1024, 1)} KB"
  defp format_bytes(bytes), do: "#{Float.round(bytes / (1024 * 1024), 1)} MB"
end
