#!/usr/bin/env bash
set -eo pipefail

# Use application binstubs
export PATH="./bin:$PATH"

announce() {
  printf "\n--- $@\n"
}

announce "Installing dependencies"
gem install bundler --conservative
bundle check || bundle install

announce "Preparing database"
if [[ $* == *--reset* ]]; then
  rails db:reset
else
  rails db:prepare
fi

announce "Removing old logs and tempfiles"
rails log:clear tmp:clear

echo
echo "Start developing with bin/dev"
