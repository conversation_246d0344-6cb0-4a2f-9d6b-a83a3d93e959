<!DOCTYPE html>
<html>
  <head>
    <title>That didn’t work (422)</title>
    <meta charset="UTF-8">
    <style>
      *,
      *::before,
      *::after {
        box-sizing: border-box;
      }

      body {
        --lch-black: 0% 0 0;
        --lch-gray: 75% 0.005 96;
        --lch-white: 100% 0 0;

        --color-border: oklch(var(--lch-gray));
        --color-bg: oklch(var(--lch-white));
        --color-text: oklch(var(--lch-black));

        --btn-size: 2.65em;

        background-color: var(--color-bg);
        block-size: 100dvh;
        color: var(--color-text);
        display: grid;
        margin: 0;
        padding: 0;
        place-items: center;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";

        @media (prefers-color-scheme: dark) {
          --lch-black: 100% 0 0;
          --lch-gray: 44.95% 0 0;
          --lch-white: 0% 0 0;
        }
      }

      svg {
        fill: var(--color-text);

        @media (prefers-color-scheme: dark) {
          fill: var(--color-text);
        }
      }

      .error {
        align-items: center;
        display: flex;
        flex-direction: column;
        gap: 2em;
        justify-content: start;
        margin-block-end: 15dvh;
      }

      .error__img {
        aspect-ratio: 1;
        background-color: var(--color-text);
        block-size: auto;
        border-radius: 50%;
        display: grid;
        place-items: center;
        inline-size: 30dvh;

        svg {
          fill: var(--color-bg);
          grid-area: 1/1;
          max-inline-size: 66%;

          @media (prefers-color-scheme: dark) {
            fill: var(--color-bg);
          }
        }
      }

      .buttons {
        display: flex;
        flex-direction: row;
        gap: 1em;
        justify-content: center;
      }

      .btn {
        align-items: center;
        aspect-ratio: 1;
        background-color: var(--color-bg);
        block-size: var(--btn-size);
        border-radius: 50%;
        border: 1px solid var(--color-border);
        color: var(--color-text);
        cursor: pointer;
        display: grid;
        font-weight: 600;
        font-size: 1.4rem;
        gap: 0.5em;
        inline-size: var(--btn-size);
        justify-content: center;
        padding: 0;
        place-items: center;
        text-align: center;

        svg {
          -webkit-touch-callout: none;
          grid-area: 1/1;
          inline-size: 1.3em;
          max-inline-size: unset;
          user-select: none;
        }
      }

      .for-screen-reader {
        clip-path: inset(50%);
        height: 1px;
        width: 1px;
        overflow: hidden;
        position: absolute;
        white-space: nowrap;
      }
    </style>
  </head>

  <body>
    <div class="error">
      <div class="error__img">
        <svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" aria-label="That didn’t work" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px"
          y="0px" viewBox="0 0 24 24" style="enable-background:new 0 0 24 24;" xml:space="preserve">
          <g>
            <circle cx="11.5" cy="11.5" r="1.8" />
            <path d="M12.2,18.7c-0.2,0-0.5,0-0.7,0c-4,0-7.2-3.2-7.2-7.2c0-4,3.2-7.2,7.2-7.2c4,0,7.2,3.2,7.2,7.2c0,1-0.2,1.9-0.5,2.7l0.3-0.3
        		c0-1.3,0.8-2.5,2-3c-0.3-4.7-4.2-8.4-9-8.4c-5,0-9,4-9,9c0,4.6,3.5,8.5,8,9L12.2,18.7z" />
            <path d="M11.5,6.1c-3,0-5.4,2.4-5.4,5.4s2.4,5.4,5.4,5.4s5.4-2.4,5.4-5.4S14.5,6.1,11.5,6.1z M11.5,15.1c-2,0-3.6-1.6-3.6-3.6
        		s1.6-3.6,3.6-3.6s3.6,1.6,3.6,3.6S13.5,15.1,11.5,15.1z" />
          </g>
          <path d="M15,24l4-4v-1.5l2.6-2.6C21.7,16,21.9,16,22,16c1.1,0,2-0.9,2-2s-0.9-2-2-2c-1.1,0-2,0.9-2,2c0,0.1,0,0.3,0.1,0.4L17.5,17
        	H16l-4,4h3V24" />
        </svg>
      </div>
      <div class="buttons">
        <a href="/" class="btn" autofocus="true">
          <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path
              d="m21.864 9.5h-11.607a.25.25 0 0 1 -.174-.43l3.864-3.721a2.609 2.609 0 0 0 -.075-3.682 2.612 2.612 0 0 0 -3.68-.077l-9.792 9.699a1 1 0 0 0 -.008 1.411l9.625 9.724a2.66 2.66 0 0 0 3.755-3.757l-3.729-3.733a.25.25 0 0 1 .177-.427h11.673c1.556 0 2-1.675 2-2.51a2.28 2.28 0 0 0 -2.029-2.497z" />
          </svg>
          <span class="for-screen-reader">Go back</span>
        </a>
        <a href="#" class="btn" autofocus="true" onclick="window.location.reload()">
          <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path
              d="m6.177 6.167a8.233 8.233 0 0 1 8.351-2.027 1.249 1.249 0 1 0 .76-2.38 10.751 10.751 0 0 0 -13.242 14.273.248.248 0 0 1 -.094.3l-1.4.922a1 1 0 0 0 .348 1.816l4.407.908a.99.99 0 0 0 .2.021 1 1 0 0 0 .979-.8l.914-4.406a1 1 0 0 0 -1.529-1.037l-1.339.881a.25.25 0 0 1 -.376-.133 8.269 8.269 0 0 1 2.021-8.338z" />
            <path
              d="m23.883 5.832a1 1 0 0 0 -.763-.807l-4.388-1a1 1 0 0 0 -1.2.752l-1 4.387a1 1 0 0 0 1.507 1.069l1.443-.906a.247.247 0 0 1 .218-.027.252.252 0 0 1 .153.159 8.249 8.249 0 0 1 -10.285 10.424 1.25 1.25 0 1 0 -.737 2.388 10.75 10.75 0 0 0 13.154-14.271.248.248 0 0 1 .1-.3l1.346-.846a1 1 0 0 0 .452-1.022z" />
          </svg>
          <span class="for-screen-reader">Try again</span>
        </a>
      </div>
    </div>
  </body>
</html>
