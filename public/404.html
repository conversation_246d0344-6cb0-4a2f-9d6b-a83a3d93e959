<!DOCTYPE html>
<html>

<head>
  <title>We can’t find that (404)</title>
  <meta charset="UTF-8">
  <style>
    *,
    *::before,
    *::after {
      box-sizing: border-box;
    }

    body {
      --lch-black: 0% 0 0;
      --lch-gray: 75% 0.005 96;
      --lch-white: 100% 0 0;

      --color-border: oklch(var(--lch-gray));
      --color-bg: oklch(var(--lch-white));
      --color-text: oklch(var(--lch-black));

      --btn-size: 2.65em;

      background-color: var(--color-bg);
      block-size: 100dvh;
      color: var(--color-text);
      display: grid;
      margin: 0;
      padding: 0;
      place-items: center;
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";

      @media (prefers-color-scheme: dark) {
        --lch-black: 100% 0 0;
        --lch-gray: 44.95% 0 0;
        --lch-white: 0% 0 0;
      }
    }

    svg {
      fill: var(--color-text);

      @media (prefers-color-scheme: dark) {
        fill: var(--color-text);
      }
    }

    .error {
      align-items: center;
      display: flex;
      flex-direction: column;
      gap: 2em;
      justify-content: start;
      margin-block-end: 15dvh;
    }

    .error__img {
      aspect-ratio: 1;
      background-color: var(--color-text);
      block-size: auto;
      border-radius: 50%;
      display: grid;
      place-items: center;
      inline-size: 30dvh;

      svg {
        fill: var(--color-bg);
        grid-area: 1/1;
        max-inline-size: 66%;

        @media (prefers-color-scheme: dark) {
          fill: var(--color-bg);
        }
      }
    }

    .buttons {
      display: flex;
      flex-direction: row;
      gap: 1em;
      justify-content: center;
    }

    .btn {
      align-items: center;
      aspect-ratio: 1;
      background-color: var(--color-bg);
      block-size: var(--btn-size);
      border-radius: 50%;
      border: 1px solid var(--color-border);
      color: var(--color-text);
      cursor: pointer;
      display: grid;
      font-weight: 600;
      font-size: 1.4rem;
      gap: 0.5em;
      inline-size: var(--btn-size);
      justify-content: center;
      padding: 0;
      place-items: center;
      text-align: center;

      svg {
        -webkit-touch-callout: none;
        grid-area: 1/1;
        inline-size: 1.3em;
        max-inline-size: unset;
        user-select: none;
      }
    }

    .for-screen-reader {
      clip-path: inset(50%);
      height: 1px;
      width: 1px;
      overflow: hidden;
      position: absolute;
      white-space: nowrap;
    }

  </style>
</head>

<body>
  <div class="error">
    <div class="error__img">
      <svg enable-background="new 0 0 24 24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" aria-label="Not found">
        <path
          d="m24 4.8c0-1.7-1.3-3-3-3h-18c-1.7 0-3 1.3-3 3v14.5c0 1.7 1.3 3 3 3h18c1.7 0 3-1.3 3-3zm-14.3-1c.3-.5.8-.6 1.3-.*******.2.4.4.*******.2.5 0 .2-.1.4-.2.5-.3.5-.8.6-1.3.4-.2-.2-.4-.3-.4-.4-.1-.2-.2-.4-.2-.6 0-.1.1-.3.2-.4zm-3.5 0c.2-.5.8-.7 1.3-.*******.2.4.4 0 .*******.4 0 .2-.1.4-.2.5-.2.5-.8.7-1.3.4-.1-.1-.3-.2-.3-.3-.1-.2-.2-.4-.2-.6 0-.1.1-.3.2-.4zm-3.6 0c.2-.4.5-.6.9-.6s.7.2.8.5c.*******.2.5 0 .2-.1.4-.2.5-.2.5-.8.7-1.3.4-.1-.1-.3-.2-.3-.3-.1-.2-.2-.4-.2-.6 0-.1 0-.2.1-.4zm19.4 15.4c0 .6-.4 1-1 1h-18c-.6 0-1-.4-1-1v-12.2c0-.1.1-.2.2-.2h19.5c.2 0 .3.1.3.2z" />
        <path
          d="m16.4 10.9c.2-.2.2-.6 0-.9l-.9-.9c-.2-.2-.6-.2-.9 0l-2.6 2.6h-.1l-2.5-2.6c-.2-.2-.6-.2-.9 0l-.9.9c-.2.2-.2.6 0 .9l2.6 2.6v.1l-2.6 2.6c-.2.2-.2.6 0 .9l.9.9c.2.2.6.2.9 0l2.6-2.6h.1l2.6 2.6c.2.2.6.2.9 0l.9-.9c.2-.2.2-.6 0-.9l-2.6-2.6v-.1z" />
      </svg>
    </div>
    <a href="/" class="btn" autofocus="true">
      <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
        <path
          d="m21.864 9.5h-11.607a.25.25 0 0 1 -.174-.43l3.864-3.721a2.609 2.609 0 0 0 -.075-3.682 2.612 2.612 0 0 0 -3.68-.077l-9.792 9.699a1 1 0 0 0 -.008 1.411l9.625 9.724a2.66 2.66 0 0 0 3.755-3.757l-3.729-3.733a.25.25 0 0 1 .177-.427h11.673c1.556 0 2-1.675 2-2.51a2.28 2.28 0 0 0 -2.029-2.497z" />
      </svg>
      <span class="for-screen-reader">Go back</span>
    </a>
  </div>
</body>

</html>
