<% content_for(:title) { page_title(@leaf, @book) } %>

<% content_for :header do %>
  <%= render "leaves/header", book: @book, leaf: @leaf %>
<% end %>

<% content_for :sidebar do %>
  <%= render "leaves/sidebar", book: @book %>
<% end %>

<% if @leaf.section? %>
  <div class="page--section <%= "theme--dark" if @leaf.leafable.theme == "dark" %>">
    <h1><%= highlight_searched_content @leaf, simple_format(@leaf.section.body), params[:search] %></h1>
  </div>
<% elsif @leaf.page? %>
  <div class="page--page" data-controller="scroll-to-highlight">
    <%= highlight_searched_content @leaf, sanitize_content(@leaf.page.body.to_html), params[:search] %>
  </div>
<% elsif @leaf.picture? %>
  <figure class="page--picture flex flex-column align-center gap margin-none">
    <% if @leaf.picture.image.attached? %>
      <%= link_to rails_blob_path(@leaf.picture.image), data: {
            action: "lightbox#open:prevent",
            lightbox_target: "image",
            lightbox_url_value: rails_blob_path(@leaf.picture.image, disposition: "attachment", only_path: true) } do %>
        <%= image_tag @leaf.picture.image.variant(:large), loading: "lazy" %>
      <% end %>
    <% else %>
      <%= image_tag "default-picture.webp", alt: "No image uploaded", loading: "lazy" %>
    <% end %>

    <figcaption>
      <%= simple_format @leaf.picture.caption %>
    </figcaption>
  </figure>
<% end %>

<% content_for :footer do %>
  <%= render "leaves/navigation", leaf: @leaf %>
<% end %>
