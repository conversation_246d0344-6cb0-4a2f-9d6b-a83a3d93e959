<nav>
  <%= render "leaves/sidebar_toggle" %>

  <%= link_to_previous_leafable(leaf, hotkey: false, for_edit: true) %>

  <div class="breadcrumbs">
    <%= render "books/index_link" %>
    <span class="flex-item-no-shrink">▸</span>
    <%= link_to book.title, book_slug_path(book) %>
    <span class="flex-item-no-shrink">▸</span>
    <%= form_with model: [ book, leaf ], url: leafable_path(leaf), class: "flex align-center gap-half", format: :html do |form| %>
      <strong class="min-width"><%= form.text_field :title, form: "leafable-editor", class: "input full-width", autocomplete: "off" %></strong>
    <% end %>
  </div>

  <button class="btn" data-action="fullscreen#toggle" data-fullscreen-target="button">
    <%= image_tag "expand.svg", aria: { hidden: true }, size: 24 %>
    <span class="for-screen-reader">Enter fullscreen</span>
  </button>

  <%= render "books/searches/search", book: @book %>
</nav>
