<% cache book do %>
  <figure class="library__book <%= "theme--#{book&.theme}" unless book.cover.attached? %>">
    <div class="flex flex-column gap-half">
      <div class="flex-inline position-relative center">
        <% if book.cover.attached? %>
          <%= image_tag book.cover, alt: "Book cover", class: "book__cover" %>
        <% else %>
          <span class="book__cover-wrapper">
            <%= image_tag "empty-cover.png", alt: "Book cover", class: "book__cover margin-block-none center" %>
            <span class="book__title overflow-line-clamp pad txt-align-start txt-tight-lines" style="--lines: 6" aria-hidden="true"><%= book.title %></span>
          </span>
        <% end %>
        <%= turbo_frame_tag dom_id(book, :bookmark), src: book_bookmark_path(book), target: "_top" %>
      </div>
      <h2 class="margin-none flex flex-column txt-normal txt-tight-lines txt-medium--responsive">
        <strong><%= book.title %></strong>
        <span class="overflow-line-clamp"><%= book.author %></span>
      </h2>
    </div>
  </figure>
<% end %>
