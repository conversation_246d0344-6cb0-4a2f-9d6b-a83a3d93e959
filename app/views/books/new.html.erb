<% content_for(:title) { "Create a new book" } %>

<% content_for :header do %>
  <nav>
    <%= link_to root_path, class: "btn flex-item-justify-start" do %>
      <%= image_tag "arrow-left.svg", aria: { hidden: true }, size: 24 %>
      <span class="for-screen-reader">Cancel and go back</span>
    <% end %>
  </nav>
<% end %>

<%= render "books/form", book: @book, users: @users, creating_user: Current.user %>

<% content_for :footer do %>
  <nav class="new-book-btn flex justify-center pad">
    <button type="submit" form="book-editor", class="btn btn--reversed txt-medium--responsive" aria-label="Create book" title="Create book">
      <%= image_tag "arrow-right.svg", aria: { hidden: true }, size: 24 %>
    </button>
  </nav>
<% end %>
