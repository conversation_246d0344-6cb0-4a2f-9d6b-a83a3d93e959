<div class="flex align-center gap txt-medium--responsive" data-controller="dependent-checkbox">
  <span class="flex align-center gap-half">
    <% if user.can_administer? %>
      <%= image_tag "crown.svg", size: 18, aria: { hidden: "true" }, class: "colorize--black" %>
    <% end %>
    <span class="overflow-ellipsis txt-medium--responsive"><%= user.name %></span>
  </span>
  <hr class="flex-item-grow margin-none" aria-hidden="true" style="--border-style: dashed">

  <fieldset class="flex align-center gap borderless unpad margin-none">
    <legend class="for-screen-reader"><%= user.name %></legend>
    <label class="btn btn--small flex-item-no-shrink">
      <%= check_box_tag "editor_ids[]", user.id, book.editable?(user: user) || user == creating_user || user.can_administer?, id: nil, disabled: user.current? || user.can_administer?, data: { action: "dependent-checkbox#input", dependent_checkbox_target: "dependant" }, aria: { label: "Role: Writer" } %>
      <%= image_tag "write.svg", size: 24, aria: { hidden: "true" } %>
      <span class="for-screen-reader"></span>
    </label>

    <label class="btn btn--small flex-item-no-shrink book-access__reader">
      <%= check_box_tag "reader_ids[]", user.id, book.accessable?(user: user) || user == creating_user || user.can_administer?, id: nil, disabled: user.current? || user.can_administer?, data: { action: "dependent-checkbox#input", dependent_checkbox_target: "dependee" }, aria: { label: "Role: Reader" } %>
      <%= image_tag "eye.svg", size: 24, aria: { hidden: "true" } %>
      <span class="for-screen-reader"></span>
    </label>
  </fieldset>
</div>
