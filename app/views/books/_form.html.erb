<%= form_with model: book, id: "book-editor" do |form| %>
  <%= tag.div class:"book__form flex align-center gap full-width #{"theme--#{book&.theme}" unless book.cover.attached? }", style:"--input-padding: 0.5rem 1rem; --input-border-radius: 0.5rem" do %>
    <div class="flex gap-half">
      <fieldset class="flex flex-column unpad margin-block-end borderless justify-space-between">
        <legend class="for-screen-reader">Cover color</legend>

        <% Book.themes.keys.each do | theme | %>
          <label class="btn btn--circle txt-small" style="--btn-background: var(--theme-color--<%= theme -%>)" >
            <%= form.radio_button :theme, theme %>
            <%= image_tag "check.svg", aria: { hidden: "true" }, size: 24, class: "checked" %>
            <span class="for-screen-reader"><%= theme %></span>
          </label>
        <% end %>
      </fieldset>
      <%= tag.div class: "flex flex-column", data: { controller: "upload-preview", upload_preview_default_image_value: asset_url("empty-cover.png") } do %>
        <label class="align-center center gap position-relative margin-block-end">
          <% unless book.cover.attached? %>
            <span class="btn btn--reversed txt-medium book__cover--add">
              <%= image_tag "camera.svg", aria: { hidden: "true" }, size: 24 %>
              <span class="for-screen-reader">Upload a cover</span>
            </span>
          <% end %>

          <div class="input--file">
            <%= image_tag book.cover.attached? ? book.cover : "empty-cover.png", alt: "Book cover",
                class: "book__cover margin-none", style: "--cover-height: 60vh",
                data: { upload_preview_target: "image" } %>
            <%= form.file_field :cover, class: "input", accept: "image/png, image/jpeg, image/jpg, image/webp",
                data: { upload_preview_target: "input", action: "upload-preview#previewImage" },
                title: book.cover.attached? ? "Replace book cover" : "Upload book cover" %>
          </div>
        </label>

        <% if book.cover.attached? %>
          <%= tag.label class:"btn btn--negative txt-small center book__cover--remove", data: { action: "click->upload-preview#clear", upload_preview_target: "button" } do %>
            <%= image_tag "minus.svg", aria: { hidden: "true" }, size: 24 %>
            <%= check_box_tag "remove_cover", "true" %>
            <span class="for-screen-reader">Remove cover image</span>
          <% end %>
        <% end %>
      <% end %>
    <% end %>

    <div class="flex flex-column gap full-width">
      <div class="flex align-center gap txt-medium">
        <%= translation_button(:book_title) %>
        <h1 class="txt-xx-large margin-none full-width">
          <%= form.text_field :title, required: true, autofocus: true, class: "input", placeholder: "Book title", autocomplete: "off" %>
        </h1>
      </div>
      <div class="flex align-center gap txt-medium">
        <%= translation_button(:book_subtitle) %>
        <small class="txt-normal txt-large txt-tight-lines full-width"><%= form.text_area :subtitle, class: "input", placeholder: "Subtitle", autocomplete: "off" %></small>
      </div>
      <div class="flex align-center gap txt-medium">
        <%= translation_button(:book_author) %>
        <small class="txt-normal txt-large txt-tight-lines full-width"><%= form.text_field :author, class: "input", placeholder: "Author", autocomplete: "off" %></small>
      </div>
    </div>
  </div>

  <div class="book-access border-radius center pad-double fill-shade flex flex-column gap margin-block-double">
    <div class="flex align-center gap txt-medium--responsive">
      <%= image_tag "eye.svg", aria: { hidden: true }, size: 36, class: "colorize--black" %>

      <div class="min-width">
        <div class="overflow-ellipsis fill-shade"><strong>Everyone</strong></div>
      </div>

      <hr class="flex-item-grow margin-none" aria-hidden="true" style="--border-style: dashed">

      <label for="book_everyone_access" class="switch">
        <%= form.check_box :everyone_access, class: "switch__input book-access__switch" %>
        <span class="switch__btn"></span>
        <span class="for-screen-reader">Only allow some people to read this book</span>
      </label>
    </div>

    <hr class="full-width margin-block-start margin-block-end-half">

    <%= render partial: "books/accesses/access", collection: users, as: :user, locals: { book: book, creating_user: creating_user } %>
  </div>

  <%= form.submit "Save", hidden: true %>
<% end %>
