<div class="flex flex-column gap margin-block-start pad <%= book.published? ? "fill-selected" : "fill-shade" %> border-radius">
  <% if book.editable? %>
    <div class="flex align-center justify-center gap-half center">
      <%= image_tag "lock.svg", aria: { hidden: true }, size: 36, class: "colorize--black" %>
        <%= form_with model: book, url: book_publication_path(book), data: { controller: "form", action: "change->form#submit" }, html: { contents: true } do |form| %>
          <label class="switch txt-medium">
            <%= form.check_box :published, checked: book.published?, class: "switch__input" %>
            <span class="switch__btn round"></span>
            <span class="for-screen-reader">Publish this book</span>
          </label>
        <% end %>
      <%= image_tag "world.svg", aria: { hidden: true }, size: 36, class: "colorize--black" %>
    </div>
  <% end %>

  <% if book.published? %>
    <div class="flex flex-column align-center gap txt-medium">
      <% public_url = book_slug_url(book) %>

      <label class="flex flex-column gap full-width txt-align-center">
        <strong id="invite_label" class="invite-label for-screen-reader">Public link to this book</strong>
        <input type="text" class="input fill-white" id="invite_url" value="<%= public_url %>" aria-labelledby="invite_label" readonly>
      </label>

      <div class="flex align-center gap">
        <div data-controller="dialog" class="flex-inline">
          <%= tag.button class: "btn", data: { action: "dialog#open" } do %>
            <%= image_tag "qr-code.svg", aria: { hidden: "true" }, size: 24, class: "colorize--black" %>
            <span class="for-screen-reader">Show public link QR code</span>
          <% end %>

          <dialog class="dialog panel shadow" data-dialog-target="dialog">
            <%= qr_code_image(public_url) %>

            <form method="dialog" class="flex justify-center">
              <button class="btn panel__close" title="Close (esc)">
                <%= image_tag "remove.svg", aria: { hidden: true }, size: 24 %>
                <span class="for-screen-reader">Close</span>
              </button>
            </form>
          </dialog>
        </div>

        <%= button_to_copy_to_clipboard(public_url) do %>
          <%= image_tag "copy-paste.svg", aria: { hidden: "true" }, size: 24, class: "colorize--black" %>
          <span class="for-screen-reader">Copy public link</span>
        <% end %>

        <%= web_share_button(public_url, "Link to join Writebook", "Hit this link to join me in Writebook and start writing.") do %>
          <%= image_tag "share.svg", aria: { hidden: "true" }, size: 24, class: "colorize--black" %>
          <span class="for-screen-reader">Share public link</span>
        <% end %>

        <% if book.editable? %>
          <%= link_to edit_book_publication_path(book), class: "btn" do %>
            <%= image_tag "pencil.svg", aria: { hidden: "true" }, size: 24, class: "colorize--black" %>
            <span class="for-screen-reader">Edit link URL</span>
          <% end %>
        <% end %>
      </div>
    </div>
  <% end %>
</div>
