<%= turbo_frame_tag @book, :publication do %>
  <div class="flex flex-column gap margin-block-start pad fill-shade border-radius <%= "shake" if @book.errors[:slug].any? %>">
    <div class="flex flex-column align-center gap txt-medium">
      <% public_url = book_url(@book) %>

      <%= form_with model: @book, url: book_publication_path(@book), class: "max-width", data: { turbo_frame: "_top" } do |form| %>
        <%= form.hidden_field :publication, value: true %>

        <label class="flex flex-column gap full-width txt-align-center">
          <div id="public_link_label" class="flex align-center gap justify-center">
            <strong>Edit publication link</strong>
          </div>
          <span class="input input--actor flex align-center fill-white">
            <%= "#{request.host}/#{@book.id}/" %>
            <%= form.text_field :slug,
                  autofocus: true,
                  class: class_names("input"),
                  required: true,
                  pattern: "^[\\-A-Za-z0-9]+$",
                  title: "Enter letters, numbers, or hyphens only" %>
          </span>
        </label>

        <div class="flex align-center gap margin-inline margin-block-start">
          <button type="submit" class="btn btn--positive flex-item-justify-end">
            <%= image_tag "check.svg", aria: { hidden: true }, size: 24 %>
            <span class="for-screen-reader">Save</span>
          </button>

          <%= link_to book_slug_path(@book), class: "btn flex-item-justify-start" do %>
            <%= image_tag "remove.svg", aria: { hidden: true }, size: 24 %>
            <span class="for-screen-reader">Cancel</span>
          <% end %>
        </div>
      <% end %>
    </div>
  </div>
<% end %>
