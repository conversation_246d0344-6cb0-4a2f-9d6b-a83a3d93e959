<% content_for(:title) { "Edit #{@book.title}" } %>

<% content_for :header do %>
  <nav>
    <%= link_to book_slug_path(@book), class: "btn flex-item-justify-start" do %>
      <%= image_tag "arrow-left.svg", aria: { hidden: true }, size: 24 %>
      <span class="for-screen-reader">Cancel and go back</span>
    <% end %>
  </nav>
<% end %>

<%= render "books/form", book: @book, users: @users, creating_user: nil %>

<% content_for :footer do %>
  <nav class="flex align-end justify-center pad">
    <%= button_to book_path(@book), method: :delete, class: "btn btn--negative",
          data: { turbo_confirm: "Are you sure you want to delete this book? It cannot be undone." } do %>
      <%= image_tag "trash.svg", aria: { hidden: true }, size: 24 %>
      <span class="for-screen-reader">Delete <%= @book.title %></span>
    <% end %>
    <button type="submit" form="book-editor", class="new-book-btn btn btn--reversed center txt-medium--responsive" aria-label="Save changes" title="Save changes">
      <%= image_tag "check.svg", aria: { hidden: true }, size: 24 %>
    </button>
  </nav>
<% end %>
