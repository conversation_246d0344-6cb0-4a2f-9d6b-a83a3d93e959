<% content_for(:title) { "Library | Writebook" } %>
<% @layout_class = "books" %>

<% content_for :header do %>
  <nav>
    <span class="btn btn--placeholder" aria-hidden="true"></span>

    <a href="https://once.com/writebook" class="product__wordmark btn btn--plain txt-large center" target="_blank">
      <%= image_tag "writebook-icon.svg", aria: { hidden: true }, size: 24 %>
      <span>Writebook</span>
    </a>

    <% if Current.user %>
      <%= link_to users_path, class: "btn" do %>
        <%= image_tag "settings.svg", aria: { hidden: true }, size: 24 %>
        <span class="for-screen-reader">Manage people and settings</span>
      <% end %>
    <% else %>
      <%= link_to new_session_path, class: "btn" do %>
        <%= image_tag "login-keys.svg", aria: { hidden: true }, size: 24 %>
        <span class="for-screen-reader">Sign in</span>
      <% end %>
    <% end %>
  </nav>
<% end %>

<% cache [ @books, signed_in? ] do %>
  <div class="library">
    <%= render @books %>
    <%= render "books/new" if signed_in? %>
  </div>
<% end %>
