<%= link_to edit_leafable_path(leaf, search: params[:search]), class: "search__result hide_from_reading_mode txt-ink", data: { turbo_frame: "_top" } do %>
  <strong><%= leaf.title_match.html_safe %>:</strong> <%= leaf.content_match.html_safe %>
<% end %>
<%= link_to leafable_slug_path(leaf, search: params[:search]), class: "search__result hide_from_edit_mode txt-ink", data: { turbo_frame: "_top" } do %>
  <strong><%= leaf.title_match.html_safe %>:</strong> <%= leaf.content_match.html_safe %>
<% end %>
