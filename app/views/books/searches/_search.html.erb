<div data-controller="dialog" data-action="keydown.ctrl+space@document->dialog#open keydown.esc->dialog#close">
  <button data-action="click->dialog#open" class="btn" title="Search (control + space)">
    <%= image_tag "search.svg", aria: { hidden: true }, size: 24 %>
    <span class="for-screen-reader">Search</span>
  </button>

  <dialog data-dialog-target="dialog" class="search__modal dialog panel shadow">
    <form method="dialog">
      <button class="btn panel__close" title="Close (esc)">
        <%= image_tag "remove.svg", aria: { hidden: true }, size: 24 %>
        <span class="for-screen-reader">Close</span>
      </button>
    </form>

    <div class="flex align-center gap">
      <label class="flex align-center gap full-width">
        <div class="flex align-center gap input input--actor">
          <%= image_tag "search.svg", aria: { hidden: "true" }, size: 24, class: "colorize--black" %>
          <%= search_field_tag :search, nil, form: "search_form", class: "search__input input full-width txt-large", autocomplete: "off", autofocus: true, placeholder: "Find in this book…" %>
        </div>

        <button class="btn btn--reversed txt-medium" title="Submit search (enter)" form="search_form" type="submit" tabindex="-1">
          <%= image_tag "arrow-right.svg", aria: { hidden: true }, size: 24 %>
          <span class="for-screen-reader">Submit search</span>
        </button>
      </label>
    </div>

    <%= turbo_frame_tag :search do %>
      <%= render "books/searches/form", book: book %>
    <% end %>
  </dialog>
</div>
