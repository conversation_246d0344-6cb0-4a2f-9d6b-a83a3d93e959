<%= tag.div class: "flex align-center gap flex-item-no-shrink",
      data: {
        controller: "edit-mode",
        edit_mode_target_url_value: target_url,
        edit_mode_editing_class: "edit-mode",
        edit_mode_autosave_outlet: "[data-controller='autosave']",
      } do %>
  <%= image_tag "eye.svg", aria: { hidden: true }, size: 28, class: "colorize--black" %>
  <label class="switch txt-medium">
    <%= check_box_tag :edit_mode_enabled, checked: checked, class: "switch__input", data: { action: "edit-mode#change" } %>
    <span class="switch__btn round"></span>
    <span class="for-screen-reader">Editing mode</span>
  </label>
  <%= image_tag "write.svg", aria: { hidden: true }, size: 24, class: "colorize--black" %>
<% end %>
