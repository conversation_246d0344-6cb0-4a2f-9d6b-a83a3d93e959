<% content_for(:title) { "Changes to #{ @leaf.title }" } %>
<% @layout_class = "books" %>

<% content_for :header do %>
  <nav>
    <%= link_to edit_leafable_path(@leaf), class: "btn" do %>
      <%= image_tag "arrow-left.svg", aria: { hidden: true }, size: 24 %>
      <span class="for-screen-reader">Go back</span>
    <% end %>

    <div class="breadcrumbs">
      <%= render "books/index_link" %>
      <span class="flex-item-no-shrink">▸</span>
      <%= link_to @leaf.book.title, @leaf.book %>
      <span class="flex-item-no-shrink">▸</span>
      <strong><%= @leaf.title %></strong>
    </div>
  </nav>
<% end %>

<div class="library">
  <section class="txt-align-start">
    <%= turbo_frame_tag :previous_version do %>
      <header class="flex align-center justify-center gap">
        <% if @edit.previous %>
          <%= link_to page_edit_path(@leaf, @edit.previous), data: { turbo_action: :advance }, class: "btn btn--reversed txt-small" do %>
            <%= image_tag "arrow-left.svg", aria: { hidden: true }, size: 24 %>
            <span class="for-screen-reader">Previous version</span>
          <% end %>
        <% else %>
          <span class="btn btn-reversed txt-small" disabled>
            <%= image_tag "arrow-left.svg", aria: { hidden: true }, size: 24 %>
            <span class="for-screen-reader">Previous version</span>
          </span>
        <% end %>

        <h2 class="btn btn--reversed txt-medium margin-none overflow-ellipsis">
          <%= time_ago_in_words(@edit.updated_at) %> ago
        </h2>

        <% if @edit.next %>
          <%= link_to page_edit_path(@leaf, @edit.next), data: { turbo_action: :advance }, class: "btn btn--reversed txt-small" do %>
            <%= image_tag "arrow-right.svg", aria: { hidden: true }, size: 24 %>
            <span class="for-screen-reader">Next version</span>
          <% end %>
        <% else %>
          <span class="btn btn-reversed txt-small" disabled>
            <%= image_tag "arrow-right.svg", aria: { hidden: true }, size: 24 %>
            <span class="for-screen-reader">Next version</span>
          </span>
        <% end %>
      </header>

      <%= @edit.page.body.to_html %>
    <% end %>
  </section>

  <section class="page-edit__current txt-align-start">
    <header class="flex align-center gap justify-center">
      <%= link_to edit_leafable_path(@leaf), class: "btn btn--positive txt-medium center margin-none" do %>
        Current
      <% end %>
    </header>

    <%= @leaf.page.body.to_html %>
  </section>
</div>
