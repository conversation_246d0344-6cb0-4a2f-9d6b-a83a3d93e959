<% content_for(:title) { "Set up Writebook" } %>

<div class="panel shadow center margin-block-double <%="shake" if flash[:alert] %>">
  <%= image_tag "writebook-icon.svg", class: "product__logo center colorize--black", size: 130 %>
  <h1 class="margin-none-block-start margin-block-end-double">Writebook</h1>

  <%= form_with model: @user, url: first_run_path, class: "flex flex-column gap" do |form| %>
    <div class="flex align-center gap">
      <%= translation_button(:user_name) %>
      <label class="flex align-center gap input input--actor txt-large">
        <%= form.text_field :name, class: "input", autocomplete: "name", placeholder: "Name", autofocus: true, required: true, data: { "1p-ignore": true } %>
        <%= image_tag "person.svg", aria: { hidden: "true" }, size: 30, class: "colorize--black" %>
      </label>
    </div>

    <div class="flex align-center gap">
      <%= translation_button(:email_address) %>
      <label class="flex align-center gap input input--actor txt-large">
        <%= form.email_field :email_address, class: "input", autocomplete: "username", placeholder: "Email address", required: true %>
        <%= image_tag "email.svg", aria: { hidden: "true" }, size: 30, class: "colorize--black" %>
      </label>
    </div>

    <div class="flex align-center gap">
      <%= translation_button(:password) %>
      <label class="flex align-center gap input input--actor txt-large">
        <%= form.password_field :password, class: "input", autocomplete: "new-password", placeholder: "Password", required: true, maxlength: 72 %>
        <%= image_tag "password.svg", aria: { hidden: "true" }, size: 30, class: "colorize--black" %>
      </label>
    </div>
    <button type="submit" id="log_in" class="btn btn--reversed center">
      <%= image_tag "arrow-right.svg", aria: { hidden: true }, size: 24 %>
      <span class="for-screen-reader">Create your account</span>
    </button>
  <% end %>
</div>
