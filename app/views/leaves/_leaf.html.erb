<% cache [ leaf, leaf.book, leaf.book.editable? ] do %>
  <%= leaf_item_tag(leaf) do %>
    <span class="btn btn--link arrangement__handle txt-small">
      <%= image_tag "handle.svg", aria: { hidden: true }, size: 24 %>
      <span class="for-screen-reader">Move <%= leaf.title %></span>
    </span>

    <%= render "leaves/delete", leaf: leaf %>

    <div class="toc__thumbnail <%= "toc__thumbnail--dark" if leaf.section&.theme == "dark" %>">
      <% if leaf.book.editable? %>
        <%= link_to edit_leafable_path(leaf), class: "toc__link hide_from_reading_mode", data: { turbo_frame: "_top" } do %>
          <span class="for-screen-reader">Edit <%= leaf.title %></span>
        <% end %>

        <%= link_to leafable_slug_path(leaf), class: "toc__link hide_from_edit_mode", data: { turbo_frame: "_top" } do %>
          <span class="for-screen-reader">Open <%= leaf.title %></span>
        <% end %>
      <% else %>
        <%= link_to leafable_slug_path(leaf), class: "toc__link", data: { turbo_frame: "_top" } do %>
          <span class="for-screen-reader">Open <%= leaf.title %></span>
        <% end %>
      <% end %>

      <%= tag.span(simple_format(leaf.section.body), class: "txt-align-center") if leaf.section? %>

      <%= sanitize_content leaf.page.html_preview if leaf.page? %>

      <% if leaf.picture? %>
        <%= image_tag leaf.leafable.image.attached? ? leaf.leafable.image.variant(:large) : "default-picture.webp" %>
      <% end %>
    </div>

    <% if leaf.book.editable? %>
      <%= link_to edit_leafable_path(leaf), class: "toc__title min-width hide_from_reading_mode", data: { turbo_frame: "_top" } do %>
        <span class="overflow-ellipsis"><%= leaf.title %></span>
      <% end %>

      <%= link_to leafable_slug_path(leaf), class: "toc__title min-width hide_from_edit_mode", data: { turbo_frame: "_top" } do %>
        <span class="overflow-ellipsis"><%= leaf.title %></span>
      <% end %>
    <% else %>
      <%= link_to leafable_slug_path(leaf), class: "toc__title min-width", data: { turbo_frame: "_top" } do %>
        <span class="overflow-ellipsis"><%= leaf.title %></span>
      <% end %>
    <% end %>

    <span class="toc__bookmark">
      <svg viewBox="0 0 64 64" width="64px" height="64px" xmlns="http://www.w3.org/2000/svg">
        <path d="m17.01 54.01v-42.98.00000045c-.00000025-1.65685 1.34315-3 3-3h24-.00000013c1.65685-.00000007 3 1.34315 3 3v42.98.0001206c0 1.10457-.895431 2-2 2-.34994 0-.693756-.0918173-.997105-.266281l-11.5039-6.61564c-.308638-.17748-.688362-.17748-.997 0l-11.5046 6.6153.00000011-.00000006c-.957413.550849-2.1801.221264-2.73095-.736149-.174564-.303405-.266442-.647312-.266451-.997351z" fill-rule="evenodd" fill="var(--color-marker)" />
      </svg>
    </span>

    <small class="toc__wordcount txt-small--responsive">
      <% if leaf.page? %>
        <%= word_count(leaf.leafable.body.content) %>
      <% end %>
    </small>
  <% end %>
<% end %>
