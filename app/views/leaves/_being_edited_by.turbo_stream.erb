<%= turbo_stream.append dom_id(leaf, :being_edited) do %>
  <%= tag.div id: dom_id(user, :being_edited_by),
        class: "flex-inline align-center justify-center margin-block-end-double txt-medium gap being-edited-by",
        data: { controller: "autoremove", action: "animationend->autoremove#remove", hide_from_user_id: user.id } do %>
    <strong class="margin-inline-end-half"><%= user.name %></strong>
    <span class="spinner txt-small flex-inline"></span>
    <%= image_tag "write.svg", aria: { hidden: true }, size: 18, class: "colorize--white margin-inline-start" %>
  <% end %>
<% end %>
