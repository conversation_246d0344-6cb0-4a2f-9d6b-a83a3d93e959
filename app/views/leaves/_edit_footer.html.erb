<% content_for :footer do %>
  <nav class="book__nav flex align-center gap">
    <span class="btn btn--placeholder flex-item-justify-start"></span>

    <%= link_to_next_leafable(leaf, hotkey: false, for_edit: true) %>

    <span class="flex-item-justify-end">
      <%= form_with url: leafable_path(leaf, format: :html), class: "flex align-center gap-half", method: :delete do |form| %>
        <%= form.button class: "btn btn--negative txt-small min-width", data: { turbo_confirm: "Are you sure you want to delete this page?" } do %>
          <%= image_tag "minus.svg", aria: { hidden: true }, size: 24 %>
          <span class="for-screen-reader">Delete <%= @leaf.title %></span>
        <% end %>
      <% end %>
    </span>
  </nav>
<% end %>
