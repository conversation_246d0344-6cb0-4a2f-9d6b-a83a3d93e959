<% content_for :header do %>
  <%= leaf_nav_tag(leaf) do %>
    <%= render "leaves/sidebar_toggle" %>

    <%= link_to_previous_leafable(leaf) %>

    <div class="breadcrumbs">
      <%= render "books/index_link" %>
      <span class="flex-item-no-shrink">▸</span>
      <%= link_to book.title, book_slug_path(book) %>
      <span class="flex-item-no-shrink">▸</span>
      <strong><%= leaf.leafable.title %></strong>
    </div>

    <button class="btn" data-action="fullscreen#toggle" data-fullscreen-target="button">
      <%= image_tag "expand.svg", aria: { hidden: true }, size: 24 %>
      <span class="for-screen-reader">Enter fullscreen</span>
    </button>

    <%= render "books/searches/search", book: @book %>
  <% end %>
<% end %>

<% content_for :toolbar do %>
  <div class="flex flex-column">
    <% if book.editable? %>
      <div class="page-toolbar fill-shade">
        <%= editing_mode_toggle_switch(@leaf, checked: false) %>
      </div>
    <% end %>

    <%= render "books/searches/banner" %>
  </div>
<% end %>
