<%= leaf_item_tag(leaf) do %>
  <span class="btn btn--link arrangement__handle txt-small">
    <%= image_tag "handle.svg", aria: { hidden: true }, size: 24 %>
    <span class="for-screen-reader">Move <%= leaf.title %></span>
  </span>

  <%= link_to leafable_path(leaf), class: "toc__thumbnail", data: { turbo_frame: "_top" } do %>
    <%= leaf.section.body if leaf.section? %>
    <%= leaf.leafable.body.to_html if leaf.page? %>
    <%= image_tag leaf.leafable.image.variant(:large) if leaf.picture&.image&.attached? %>
  <% end %>

  <div class="toc__title flex align-center min-width">
    <%= form_with model: [ leaf.book, leaf ], url: leafable_path(leaf), class: "flex align-center max-width min-width", id: dom_id(leaf, "form") do |form| %>
      <%= form.text_field :title, class: "input full-width", autofocus: true, autocomplete: "off", data: { controller: "autoselect" } %>
    <% end %>

    <span class="flex align-center gap-half">
      <button type="submit" class="btn txt-small btn--positive" form="<%= dom_id(leaf, "form") %>">
        <%= image_tag "check.svg", aria: { hidden: true }, size: 24 %>
        <span class="for-screen-reader">Save</span>
      </button>

      <%= button_to leafable_path(leaf), method: :delete, class: "btn btn--negative txt-small" do %>
        <%= image_tag "trash.svg", aria: { hidden: true }, size: 24 %>
        <span class="for-screen-reader">Delete this page</span>
      <% end %>
    </span>
  </div>

  <small class="toc__wordcount">
    <% if leaf.page? %>
      <%= word_count(leaf.leafable.body.content) %>
    <% end %>
  </small>

  <%= yield if block_given? %>
<% end %>
