<% content_for(:title) { "Edit #{ @picture.title }" } %>

<% content_for :header do %>
  <%= render "leafables/edit_header", leaf: @leaf, book: @book %>
<% end %>

<% content_for :toolbar do %>
  <div class="page-toolbar fill-selected align-center gap-half margin-block-end-double">
    <%= editing_mode_toggle_switch(@leaf, checked: true) %>

    <span class="separator margin-inline-half" aria-hidden="true"></span>

    <button type="submit" form="leafable-editor" class="btn flex page-toolbar__save flex-item-justify-end flex-item-no-shrink txt-small">
      <%= image_tag "check.svg", aria: { hidden: true }, size: 24 %>
      <span class="for-screen-reader">Save</span>
    </button>
  </div>
<% end %>

<% content_for :sidebar do %>
  <%= render "leaves/sidebar", book: @book %>
<% end %>

<div class="page--picture picture-form margin-none">
  <%= render "pictures/form", book: @book, picture: @picture %>
</div>

<%= render "leaves/edit_footer", leaf: @leaf %>
