<%= leafable_edit_form(picture, id: "leafable-editor") do |form| %>
  <label class="input input--file input--picture unpad" data-controller="upload-preview">
    <%= image_tag picture.image.attached? ? picture.image.variant(:large) : "default-picture.webp", alt: "Picture",
        data: { upload_preview_target: "image" } %>
    <%= form.file_field :image, class: "input", accept: "image/png, image/jpeg, image/jpg, image/webp", autofocus: true,
        data: { upload_preview_target: "input", action: "upload-preview#previewImage" } %>
  </label>
  <div class="flex align-center gap margin-block">
    <%= translation_button(:picture_caption) %>
    <%= form.text_field :caption, class: "input", placeholder: "Picture caption" %>
  </div>
  <%= form.submit "Save", hidden: true, data: { upload_preview_target: "button" } %>
<% end %>
