<% content_for(:title) { "Custom styles" } %>

<% content_for :header do %>
  <nav>
    <%= link_to users_path, class: "btn" do %>
      <%= image_tag "arrow-left.svg", aria: { hidden: true }, size: 24 %>
      <span class="for-screen-reader">Go back</span>
    <% end %>

    <div class="breadcrumbs">
      <%= render "books/index_link" %>
      <span class="flex-item-no-shrink">▸</span>
      <%= image_tag "art.svg", aria: { label: "Custom styles" }, size: 19, class: "colorize--black", alt: "Custom styles" %>
    </div>
  </nav>
<% end %>

<div class="center pad txt-align-center flex flex-column">
  <%= form_with model: @account, url: account_custom_styles_url, class: "flex flex-column gap",
      data: { controller: "form", action: "keydown.ctrl+enter->form#submit keydown.meta+enter->form#submit" } do |form| %>
    <div class="pad-inline-double margin-inline">
      <div class="flex align-center gap-half center full-width justify-center">
        <span class="txt-medium"><%= translation_button(:custom_styles) %></span>
        <h1 class="margin-none">Custom CSS</h1>
      </div>
      <p class="flex flex-wrap align-center justify-center gap margin-none-block-start" style="--column-gap: 0.5ch; --row-gap: 0">
        <span>Add custom CSS styles.</span>
        <%= image_tag "alert.svg", class: "flex-inline colorize--black", size: 16, aria: { hidden: "true" } %>
        <span>Use Caution: you could break things.</span>
      </p>
    </div>

    <label class="flex align-start gap flex-item-grow">
      <%= form.text_area :custom_styles, class: "input input--code txt--small", placeholder: "Add CSS styles…",
            autocomplete: "off", spellcheck: "false", autocorrect: "off", autocapitalize: "off",
            rows: 16, required: false %>
    </label>

    <%= form.button class: "btn btn--reversed center txt-large", type: "submit" do %>
      <%= image_tag "check.svg", aria: { hidden: "true" }, size: 20 %>
      <span class="for-screen-reader">Save changes</span>
    <% end %>
  <% end %>
</div>
