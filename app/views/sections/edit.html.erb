<% content_for(:title) { "Edit #{ @section.title }" } %>

<% content_for :header do %>
  <%= render "leafables/edit_header", leaf: @leaf, book: @book %>
<% end %>

<% content_for :toolbar do %>
  <div class="page-toolbar fill-selected align-center gap-half margin-block-end-double">
    <%= editing_mode_toggle_switch(@leaf, checked: true) %>

    <span class="separator margin-inline-half" aria-hidden="true"></span>

    <label class="btn txt-small">
      <%= hidden_field_tag "section[theme]", nil, id: nil, form: "leafable-editor" %>
      <%= check_box_tag "section[theme]", "dark", @section.theme.present?, class: "switch__input", form: "leafable-editor" %>
      <%= image_tag "theme-switch.svg", aria: { hidden: true }, size: 24 %>
      <span class="for-screen-reader">Change theme</span>
    </label>

    <span class="separator margin-inline-half" aria-hidden="true"></span>

    <button type="submit" form="leafable-editor" class="btn flex page-toolbar__save flex-item-justify-end flex-item-no-shrink txt-small">
      <%= image_tag "check.svg", aria: { hidden: true }, size: 24 %>
      <span class="for-screen-reader">Save</span>
    </button>
  </div>
<% end %>

<% content_for :sidebar do %>
  <%= render "leaves/sidebar", book: @book %>
<% end %>

<div class="page--section <%= "theme--dark" if @section.theme == "dark" %>">
  <%= render "sections/form", book: @book, section: @section %>
</div>

<%= render "leaves/edit_footer", leaf: @leaf %>
