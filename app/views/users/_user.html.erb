<div class="flex align-center gap-half pad-inline txt-medium--responsive">
  <strong class="overflow-ellipsis txt-medium--responsive"><%= link_to user.name, user.current? ? edit_user_profile_path(user) : user_profile_path(user) %></strong>
  <hr class="flex-item-grow margin-none" aria-hidden="true">

  <%= form_with model: user, url: user_path(user), data: { controller: "form" }, method: :patch do | form | %>
    <label class="btn btn--small flex-item-no-shrink" for="<%= dom_id(user, :role) %>">
      <span class="for-screen-reader"><%= user.name %>'s role: <%= user.administrator? ? "Administrator" : "Member" %></span>
      <%= image_tag "crown.svg", size: 24, aria: { hidden: "true" } %>
      <%= form.check_box :role, { data: { action: "form#submit" }, hidden: true, id: dom_id(user, :role), disabled: ("disabled" if user.current? || !Current.user.can_administer?) }, "administrator", "member" %>
    </label>
  <% end %>

  <%= button_to user_path(user), method: :delete, class: "btn btn--small btn--negative flex-item-no-shrink", disabled: ("disabled" if user.current? || !Current.user.can_administer?), data: {
        turbo_confirm: "Are you sure you want to permanently remove this person from the account? This can’t be undone."} do %>
    <%= image_tag "minus.svg", aria: { hidden: true }, size: 24 %>
    <span class="for-screen-reader">Remove <%= user.name %> from the account</span>
  <% end %>
</div>
