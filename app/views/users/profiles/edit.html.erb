<% content_for(:title) { "Your settings" } %>

<% content_for :header do %>
  <nav>
    <%= link_to users_path, class: "btn" do %>
      <%= image_tag "arrow-left.svg", aria: { hidden: true }, size: 24 %>
      <span class="for-screen-reader">Go back</span>
    <% end %>

    <div class="breadcrumbs">
      <%= render "books/index_link" %>
      <span class="flex-item-no-shrink">▸</span>
      <%= link_to users_path, class: "btn borderless txt-small flex-item-no-shrink" do %>
        <%= image_tag "people.svg", aria: { label: "People" }, size: 19, class: "colorize--black", alt: "People" %>
        <span class="for-screen-reader">Manage people</span>
      <% end %>
      <span class="flex-item-no-shrink">▸</span>
      <%= @user.name %>
    </div>
  </nav>
<% end %>

<div class="panel margin-block-double shadow center">
  <%= form_with model: @user, url: user_profile_path(@user), class: "flex flex-column gap" do |form| %>
    <%= form.hidden_field :role %>
    <div class="flex align-center gap margin-block-start">
      <%= translation_button(:user_name) %>
      <label class="flex align-center gap input input--actor">
        <%= form.text_field :name, class: "input full-width", autocomplete: "name", placeholder: "Name", autofocus: true, required: true, data: { "1p-ignore": true } %>
        <%= image_tag "person.svg", aria: { hidden: "true" }, size: 30, class: "colorize--black" %>
      </label>
    </div>
    <div class="flex align-center gap">
      <%= translation_button(:email_address) %>
      <label class="flex align-center gap input input--actor">
        <%= form.email_field :email_address, class: "input full-width", autocomplete: "username", placeholder: "Email address", required: true %>
        <%= image_tag "email.svg", aria: { hidden: "true" }, size: 30, class: "colorize--black" %>
      </label>
    </div>
    <div class="flex align-center gap">
      <%= translation_button(:update_password) %>
      <label class="flex align-center gap input input--actor">
        <%= form.password_field :password, class: "input full-width", autocomplete: "new-password", placeholder: "Change password", maxlength: 72 %>
        <%= image_tag "password.svg", aria: { hidden: "true" }, size: 30, class: "colorize--black" %>
      </label>
    </div>
    <button type="submit" class="btn btn--reversed center">
      <%= image_tag "check.svg", aria: { hidden: true }, size: 24 %>
      <span class="for-screen-reader">Save changes</span>
    </button>
  <% end %>
</div>

<div class="panel margin-block-double shadow center">
  <%= render "users/transfer", user: @user %>
</div>

<div class="panel margin-block-double shadow center">
  <%= button_to session_path, method: :delete, class: "btn center" do %>
    <%= image_tag "logout.svg", aria: { hidden: true }, size: 24 %>
    <span class="for-screen-reader">Sign out</span>
  <% end %>
</div>
