<% content_for(:title) { "Create your account" } %>
<% turbo_page_requires_reload %>

<% content_for :header do %>
  <nav>
    <%= link_to new_session_path, class: "btn flex-item-justify-end" do %>
      <%= image_tag "login-keys.svg", aria: { hidden: true }, size: 24 %>
      <span class="for-screen-reader">Sign in instead</span>
    <% end %>
  </nav>
<% end %>

<div class="panel shadow center <%= "shake" if flash[:alert] %>">
  <%= image_tag "writebook-icon.svg", class: "product__logo center colorize--black", size: 130 %>
  <h1 class="margin-none-block-start margin-block-end-double">Writebook</h1>

  <%= form_with model: @user, url: join_path(params[:join_code]), class: "flex flex-column gap" do |form| %>
    <div class="flex align-center gap">
      <%= translation_button(:user_name) %>
      <label class="flex align-center gap input input--actor">
        <%= form.text_field :name, class: "input full-width", autocomplete: "name", placeholder: "Name", autofocus: true, required: true, data: { "1p-ignore": true } %>
        <%= image_tag "person.svg", aria: { hidden: "true" }, size: 30, class: "colorize--black" %>
      </label>
    </div>
    <div class="flex align-center gap">
      <%= translation_button(:email_address) %>
      <label class="flex align-center gap input input--actor">
        <%= form.email_field :email_address, class: "input full-width", autocomplete: "username", placeholder: "Email address", required: true %>
        <%= image_tag "email.svg", aria: { hidden: "true" }, size: 30, class: "colorize--black" %>
      </label>
    </div>
    <div class="flex align-center gap">
      <%= translation_button(:password) %>
      <label class="flex align-center gap input input--actor">
        <%= form.password_field :password, class: "input full-width", autocomplete: "new-password", placeholder: "Password", required: true, maxlength: 72 %>
        <%= image_tag "password.svg", aria: { hidden: "true" }, size: 30, class: "colorize--black" %>
      </label>
    </div>
    <button type="submit" id="log_in" class="btn btn--reversed center">
      <%= image_tag "arrow-right.svg", aria: { hidden: true }, size: 24 %>
      <span class="for-screen-reader">Sign in</span>
    </button>
  <% end %>
</div>
