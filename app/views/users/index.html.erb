<% content_for(:title) { "People on the account" } %>

<% content_for :header do %>
  <nav>
    <%= link_to root_path, class: "btn" do %>
      <%= image_tag "arrow-left.svg", aria: { hidden: true }, size: 24 %>
      <span class="for-screen-reader">Go back</span>
    <% end %>

    <div class="breadcrumbs">
      <%= render "books/index_link" %>
      <span class="flex-item-no-shrink">▸</span>
      <%= image_tag "settings.svg", aria: { label: "Settings" }, size: 19, class: "colorize--black", alt: "Settings" %>
    </div>

    <% if Current.user.can_administer? %>
      <%= link_to edit_account_custom_styles_url, class: "btn" do %>
        <%= image_tag "art.svg", aria: { hidden: true }, size: 24 %>
        <span class="for-screen-reader">Custom styles</span>
      <% end %>
    <% end %>
  </nav>
<% end %>

<div class="panel borderless center pad fill-shade flex flex-column margin-block">
  <%= render "invite" %>
</div>

<div class="panel borderless center pad fill-none flex flex-column gap">
  <%= render @users %>
</div>

<% content_for(:footer) do %>
  <div class="txt-align-center center margin-block-double txt-subtle">Writebook&trade; version <%= version_badge %></div>
<% end %>
