<!DOCTYPE html>
<html>
  <head>
    <title><%= content_for(:title) || "Writebook" %></title>

    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta charset="UTF-8">
    <meta name="color-scheme" content="light dark">
    <meta name="theme-color" content="#ffffff" media="(prefers-color-scheme: light)">
    <meta name="theme-color" content="#000000" media="(prefers-color-scheme: dark)">
    <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no, interactive-widget=resizes-content">
    <meta name="view-transition" content="same-origin">
    <meta name="turbo-cache-control" content="no-cache">

    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>
    <% if signed_in? %>
      <%= hide_from_user_style_tag %>
    <% end %>

    <%= yield :head %>

    <link rel="manifest" href="/manifest.json">
    <link rel="icon" href="/favicon.svg" type="image/svg+xml">
    <link rel="icon" href="/favicon.png" type="image/png">
    <link rel="apple-touch-icon" href="/app-icon.png">

    <%= stylesheet_link_tag :all, "data-turbo-track": "reload" %>
    <%= custom_styles_tag %>

    <%= javascript_importmap_tags %>
  </head>

  <body data-controller="fullscreen lightbox touch">
    <header id="header">
      <%= yield :header %>
    </header>

    <div id="toolbar">
      <%= yield :toolbar %>
    </div>

    <main id="main" class="<%= @layout_class %>">
      <%= yield %>
    </main>

    <aside id="sidebar" aria-label="Table of Contents">
      <%= yield :sidebar %>
    </aside>

    <footer id="footer">
      <%= yield :footer %>
    </footer>

    <%= render "layouts/lightbox" %>
  </body>
</html>
