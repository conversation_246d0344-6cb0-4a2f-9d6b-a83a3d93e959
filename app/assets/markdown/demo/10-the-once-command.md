---
title: The ONCE command
---
# ONCE command reference

Install and manage ONCE products.

To run the `once` command in a terminal connect to your server using with either `SSH` or a web-based cloud console.

## Usage:

`once` - [command]

## Manage passwords

`password`  - manage passwords

## Manage automatic updates

`auto-update` - manage automatic updates

## Manage application data

`data` - manage application data

## Additional Commands:

`help` - Help about any command
`setup` - Re-run the initial setup of Writebook
`start` - Start Writebook
`status` - Show the current status
`stop` - Stop Writebook
`update` - Update Writebook to the latest version

## Flags:

`-h`, `--help`  - help for once

Use `once [command] --help` for more information about a command.
