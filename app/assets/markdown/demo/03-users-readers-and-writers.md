---
title: Users, Readers & Writers
---
# Users, Readers & Writers

You can use Writebook alone, or invite others to publish with you. Users invited to Writebook can read books that are private to just your team, create their own books, or you can choose which books they can contribute to.

  ![users.png](/u/users-GdhgKg.png)

## Inviting people
Your Writebook has a special join link you can share with people. When they hit the link they will be asked to enter their name, email address, and set a password for their account. Once they do, they'll be immediately signed in.

You can find the join link by hitting the people button on the index of books. There you'll see the special URL which you can share via QR code or copy it to paste into an email, text, and chat.

## Administrators

Hit the _Crown_ button next to a person to designate them as an _Administrator_. Ad<PERSON> have access to every book on the account and can do things like remove people from the account, help them get back in if they forget their password, and regenerate the join URL if it falls into the wrong hands.

## Book Permissions
For each book there are readers and writers. When you create a book you'll have the chance to choose who on the account can read it and who has permission to edit it.

 ![book-permissions.png](/u/book-permissions-QQFdnG.png)

### Readers
Readers can see the book but can't make any edits or change any settings. They must be signed in to read it if the book isn't available on the public internet. More on that in a later chapter.

### Writers
Writers all have full permission to read, edit, and change anything in the book. They can change the title, author, cover and any text or pictures. Admins are always writers.

### Everyone
Flip the _Everyone_ switch to give everyone on the account read access to the book—this includes anyone who joins the account later. This setting is ideal for something like a company handbook that you want everyone on your team to instantly have access to. You can give writer access to individuals even when everyone is on.
