---
title: Writing with Writebook
---
# Writing

We've designed Writebook to be a calm, enjoyable and distraction-free place to write.

Here's what you need to know.

## Editing mode

The first step when working on your book is to flip the switch and enter _Editing mode_. That will reveal the page toolbar and save button. Edit mode stays on even as you move around to other pages in your book while working. You can turn it off when you're done working or toggle it anytime to preview your changes as readers will see them.

 ![edit-mode.gif](/u/edit-mode-ygeCcM.gif)

## Autosave

Writebook automatically saves as you write so you don't have to think about saving or worry about losing your work. The _Save_ button in the page toolbar continually comminicates where the are unsaved changes, when it's in the process of saving, and when all changes have been saved. You can hit the button if you want to, but it'll save for you even if you don't.

 ![autosave.gif](/u/autosave-Ssql4X.gif)

## Full Screen

For more immersive, distraction-free writing hit the expand button at the top of the screen to enter fullscreen mode (on supported devices). [Full Screen](/2/the-writebook-manual/170/reading#fullscreen) hides most of the clutter and busyness of your device and operating system so you can focus on your work. _Hint: you can use Full Screen while reading, too!_

## Version history

As you work on your book, Writebook will track when you've made a batch of changes and record them as a new version. Hit the _History_ button next to the _Save_ button to browse through previous versions of any page.
