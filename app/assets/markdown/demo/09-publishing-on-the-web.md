---
title: Publishing on the web
---
# Publishing your book

Writebook can be used to publish private books just for your team, or public books available for everyone on the web.

## Private books
Private books are available to anyone who is signed in to your Writebook and has permission to read a book. See the section, [Users, Readers & Writers](/2/the-writebook-manual/29/users-readers-writers) for details.

## Publishing to the web
To make a book available to everyone on the internet, just flip the switch from locked to web. Your book will be instantly available at the URL displayed when the switch is on. There are tools below to display a QR code or copy the URL so you can share it in an email, text, or chat—or link to it on your home page.

 ![publishing.gif](/u/publishing-vP0uNP.gif)

## Public URLs
Your book's public URL consists of the domain you provided when you set up Writebook plus a _slug_ derived from the title of the book. For example, if your Writebook is at `books.37signals.com`, then the title _Getting Real_ will be published at `books.37signals.com/getting-real`. You can change this slug if you don't like the one Writebook provided.

### Redirecting
Visitors who visit your URL without any book slug (.e.g., `books.37signals.com`) will be redirected to your published book. If you have more than one book published to the web, they'll see a list of all public books on your Writebook that are published. If you don't have any published books, they'll be directed to sign in.
