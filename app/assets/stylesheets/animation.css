/* Animation classes */
.shake {
  animation: shake 400ms both;
}

.wiggle {
  animation: wiggle 400ms both;
}

/* Keyframes */
@keyframes fade-out {
  to { opacity: 0; }
}

@keyframes pulse {
  0%   { opacity: 1; }
  50%  { opacity: 0.6; }
  100% { opacity: 1; }
}

@keyframes shake {
  0%  { transform: translateX(-2rem); }
  25% { transform: translateX(2rem); }
  50% { transform: translateX(-1rem); }
  75% { transform: translateX(1rem); }
}

@keyframes submitting {
  0%    { -webkit-mask-position: 0% 0%,   50% 0%,   100% 0% }
  12.5% { -webkit-mask-position: 0% 50%,  50% 0%,   100% 0% }
  25%   { -webkit-mask-position: 0% 100%, 50% 50%,  100% 0% }
  37.5% { -webkit-mask-position: 0% 100%, 50% 100%, 100% 50% }
  50%   { -webkit-mask-position: 0% 100%, 50% 100%, 100% 100% }
  62.5% { -webkit-mask-position: 0% 50%,  50% 100%, 100% 100% }
  75%   { -webkit-mask-position: 0% 0%,   50% 50%,  100% 100% }
  87.5% { -webkit-mask-position: 0% 0%,   50% 0%,   100% 50% }
  100%  { -webkit-mask-position: 0% 0%,   50% 0%,   100% 0% }
}

@keyframes success {
  0%  { background-color: var(--color-border-darker); scale: 0.8; }
  20% { background-color: var(--color-border-darker); scale: 1; }
}

@keyframes wiggle {
  0%   { transform: rotate(0deg); }
  20%  { transform: rotate(3deg); }
  40%  { transform: rotate(-3deg); }
  60%  { transform: rotate(3deg); }
  80%  { transform: rotate(-3deg); }
  100% { transform: rotate(0deg); }
}

@keyframes zoom-fade {
  100% { transform: translateY(-2em); opacity: 0; }
}

/* View Transitions */
::view-transition-old(root) {
  animation:
    100ms cubic-bezier(0.4, 0, 1, 1) both fade-out,
    300ms cubic-bezier(0.4, 0, 0.2, 1) both slide-to-left;
}

::view-transition-new(root) {
  animation:
    200ms cubic-bezier(0, 0, 0.2, 1) 90ms both fade-in,
    300ms cubic-bezier(0.4, 0, 0.2, 1) both slide-from-right;
}
