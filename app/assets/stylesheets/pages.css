#main:has(.page--section, .page--picture) {
  display: grid;
}

#main:has(.page--picture:not(.picture-form)) {
  inline-size: 100%;
  padding: 0;

  figcaption {
    inline-size: 67ch;
    margin-inline: auto;
    max-inline-size: 100dvw;
    padding-inline: clamp(var(--inline-space), 5%, calc(var(--inline-space) * 3));
  }
}

:has(#section_theme[value=dark]:checked),
:not(:has(#section_theme[value=dark])):has(.page--section.theme--dark) {
  --lch-black: 100% 0 0;
  --lch-white: 0% 0 0;
  --lch-gray-light: 25.2% 0 0;
  --lch-gray: 30.12% 0 0;
  --lch-gray-dark: 44.95% 0 0;
  --lch-blue: 72.25% 0.16 248;
  --lch-blue-light: 28.11% 0.053 248;
  --lch-blue-dark: 42.25% 0.07 248;
  --lch-red: 73.8% 0.184 29.18;
  --lch-green: 75% 0.21 141.89;
  --lch-green-light: 28.11% 0.02 142.49;
  --lch-yellow: 40.9% 0.06 88.9;

  @media (prefers-color-scheme: dark) {
    --lch-black: 0% 0 0;
    --lch-white: 100% 0 0;
    --lch-gray-light: 96% 0.005 96;
    --lch-gray: 92% 0.005 96;
    --lch-gray-dark: 75% 0.005 96;
    --lch-blue: 54% 0.15 255;
    --lch-blue-light: 95% 0.03 255;
    --lch-blue-dark: 80% 0.08 255;
    --lch-orange: 70% 0.2 44;
    --lch-red: 51% 0.2 31;
    --lch-green: 65.59% 0.234 142.49;
    --lch-green-light: 95% 0.03 142.49;
    --lch-always-black: 0% 0 0;
    --lch-always-white: 100% 0 0;
    --lch-yellow: 92.62% 0.1 91.5;
  }

  img,
  .btn img {
    filter: invert(1);

    @media (prefers-color-scheme: dark) {
      filter: invert(0);
    }
  }

  .btn:has(input:checked),
  .btn:is(.btn--positive, .btn--negative, .btn--reversed) {
    & img {
      filter: invert(0);
    }

    @media (prefers-color-scheme: dark) {
      & img {
        filter: invert(1);
      }
    }
  }
}


.page-toolbar {
  --padding-block: 0.5em;
  --padding-inline: 1.1em;

  border-radius: 5em;
  display: inline-flex;
  margin: var(--block-space-half) auto 0;
  min-width: 0;
  padding: var(--padding-block) var(--padding-inline);
  transition: background-color 300ms ease;

  .separator {
    block-size: 100%;
    border-color: var(--color-bg);
    border-width: 3px;
    margin-block: calc(-1 * var(--padding-block));
  }

  .btn {
    --btn-border-color: transparent;

    flex-shrink: 0;
  }
}

:is(.page__body, .page--page) {
  text-align: start;
}

:is(.page--section, .page--picture) {
  place-self: center;
}

  body:has(#leafable-editor.clean) {
    & .page-toolbar__save {
      --btn-background: var(--color-positive);
      --btn-border-color: var(--color-bg);
      --btn-color: var(--color-ink-reversed);
      --outline-color: var(--color-positive);

      img {
        filter: invert(1);

        @media (prefers-color-scheme: dark) {
          filter: invert(0);
        }
      }
    }
  }

  body:has(#leafable-editor.dirty) {
    & .page-toolbar__save {
      --btn-background: var(--color-link);
      --btn-border-color: var(--color-bg);
      --btn-color: var(--color-ink-reversed);
      --outline-color: var(--color-link);

      img {
        filter: invert(1);

        @media (prefers-color-scheme: dark) {
          filter: invert(0);
        }
      }
    }
  }

  body:has(#leafable-editor.saving) .page-toolbar__save {
    position: relative;

    > * {
      visibility: hidden;
    }

    &::after {
      --mask: no-repeat radial-gradient(#000 68%,#0000 71%);
      --size: 1.25em;

      -webkit-mask: var(--mask), var(--mask), var(--mask);
      -webkit-mask-size: 28% 45%;
      animation: submitting 1s infinite linear;
      aspect-ratio: 8/5;
      background: currentColor;
      content: "";
      inline-size: var(--size);
      inset: 50%;
      margin-block: calc((var(--size) / 3) * -1);
      margin-inline: calc((var(--size) / 2) * -1);
      position: absolute;
    }
  }

body.edit-mode .hide_from_edit_mode {
  display: none;
}

body:not(.edit-mode) .hide_from_reading_mode {
  display: none;
}

.being-edited-by {
  animation: fade-out 300ms ease-out 20s;
  background-color: var(--color-ink);
  border-radius: 2em;
  color: var(--color-ink-reversed);
  padding: 0.5em 1em;
}

.page-edit__current {
  @media (max-width: 70ch) {
    display: none;
  }
}
