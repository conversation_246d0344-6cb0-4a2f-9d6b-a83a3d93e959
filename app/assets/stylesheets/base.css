html, body {
  --font-sans: system-ui;
  --font-serif: ui-serif, serif;
  --font-mono: ui-monospace, monospace;
  --hover-color: var(--color-subtle-dark);
  --hover-size: 0.15rem;
  --hover-filter: brightness(1);

  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  -webkit-text-size-adjust: none;
  background: var(--color-bg);
  color: var(--color-ink);
  font-family: var(--font-sans);
  line-height: 1.4;
  overflow: unset;
  scroll-behavior: auto;
  text-rendering: optimizeLegibility;
  text-size-adjust: none;
}

a:not([class]) {
  --hover-size: 0;

  color: var(--color-link);
  text-decoration: underline;
  text-decoration-skip-ink: auto;
}

:is(a, button, input, textarea, .switch, .toc, .toc__title) {
  --outline-size: max(2px, 0.08em);

  caret-color: var(--color-link);
  text-decoration: none;
  touch-action: manipulation;
  transition: box-shadow 150ms ease, outline-offset 150ms ease, background-color 150ms ease, opacity 150ms ease, filter 150ms ease;

  /* Hover */
  @media (any-hover: hover) {
    &:where(:not(:active):hover) {
      box-shadow: 0 0 0 var(--hover-size) var(--hover-color);
    }
  }

  /* Keyboard navigation */
  &:where(:not(:active)):focus-visible {
    outline-width: var(--outline-size);
    outline-color: var(--outline-color, currentColor);
    outline-offset: var(--outline-offset, calc(var(--outline-size) * 2));
  }

  &:where(:focus-visible):active {
    outline: 0;
  }

  /* Pressing */
  &:focus:not(:focus-visible) {
    --outline-offset: 0;
  }

  /* Disabled */
  &:where([disabled]):not(:hover):not(:active) {
    cursor: not-allowed;
    filter: brightness(0.75);
  }
}

::selection {
  background-color: var(--color-selected);
}

:where(ul, ol):where([role="list"]) {
  margin: 0;
  padding: 0;
  list-style: none;
}

/* Printing */
@page {
  margin: 1in;
}

@media print {
  .no-print {
    display: none;
  }
}

/* Turbo */
turbo-frame {
  display: contents;
}

/* Nicer scrollbars on Chrome 29+. This is intended for Windows machines, but */
/* there's not a way to target Windows using CSS, so Chrome on Mac will have */
/* slightly thinner scrollbars than normal. #C1C1C1 is the default color on Macs. */
@media screen and (-webkit-min-device-pixel-ratio:0) and (min-resolution:.001dpcm) {
  * {
    scrollbar-color: #C1C1C1 transparent;
    scrollbar-width: thin;
  }
}
