/*
 * Modern CSS Reset
 * @link https://github.com/hankchizljaw/modern-css-reset
*/

/* Box sizing rules */
*,
*::before,
*::after {
  box-sizing: border-box;
}

/* Remove default margin */
body,
h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0;
}

p,
li,
h1,
h2,
h3,
h4 {
  /* Help prevent overflow of long words/names/URLs */
  word-break: break-word;

  /* Optional, not supported for all languages */
  /* hyphens: auto; */
}

html,
body {
  overflow-x: hidden;
}

html {
  scroll-behavior: smooth;
}

/* Set core body defaults */
body {
  min-height: 100dvh;
  font-family: sans-serif;
  font-size: 100%;
  line-height: 1.5;
  text-rendering: optimizeSpeed;
}

/* Make images easier to work with */
img {
  display: block;
  max-inline-size: 100%;
}

/* Inherit fonts for inputs and buttons */
input,
button,
textarea,
select {
  font: inherit;
}

/* Remove all animations and transitions for people that prefer not to see them */
@media (prefers-reduced-motion: reduce) {

  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  html {
    scroll-behavior: initial;
  }
}
