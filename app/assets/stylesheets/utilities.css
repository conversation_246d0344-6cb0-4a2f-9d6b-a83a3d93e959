:root {
  --inline-space: 1ch;
  --inline-space-half: calc(var(--inline-space) / 2);
  --inline-space-double: calc(var(--inline-space) * 2);

  --block-space: 1rem;
  --block-space-half: calc(var(--block-space) / 2);
  --block-space-double: calc(var(--block-space) * 2);

  --font-small-responsive: clamp(0.8rem, 2cqi, 1rem);
  --font-medium-responsive: clamp(1rem, 2.5cqi, 1.4rem);
  --font-large-responsive: clamp(1.3rem, 4cqi, 1.8rem);
  --font-x-large-responsive: clamp(1.8rem, 5cqi, 3.2rem);
}

/* Text */
.txt-small { font-size: 0.8rem; }
.txt-medium { font-size: 1rem; }
.txt-large { font-size: 1.4rem; }
.txt-x-large { font-size: 1.8rem; }
.txt-xx-large { font-size: 2.4rem; }

.txt-small--responsive { font-size: var(--font-small-responsive); }
.txt-medium--responsive { font-size: var(--font-medium-responsive); }
.txt-large--responsive { font-size: var(--font-large-responsive); }
.txt-x-large--responsive { font-size: var(--font-x-large-responsive); }

.txt-align-center { text-align: center; }
.txt-align-start { text-align: start; }
.txt-align-end { text-align: end; }

.txt-ink { color: var(--color-ink); }
.txt-reversed { color: var(--color-ink-reversed); }
.txt-negative { color: var(--color-negative); }
.txt-subtle { color: var(--color-subtle-dark); }
.txt-undecorated { text-decoration: none; }
.txt-tight-lines { line-height: 1.2; }
.txt-normal { font-weight: 400; font-style: normal; }
.txt-nowrap { white-space: nowrap; }
.txt-uppercase { text-transform: uppercase; }

/* Flexbox and Grid */
.justify-end { justify-content: end; }
.justify-start { justify-content: start; }
.justify-center { justify-content: center; }
.justify-space-between { justify-content: space-between; }

.align-center { align-items: center; }
.align-start { align-items: start; }
.align-end { align-items: end; }

.align-self-start { align-self: start; }

.contain { contain: inline-size; }

.flex { display: flex; }
.flex-inline { display: inline-flex; }
.flex-column { flex-direction: column; }
.flex-wrap { flex-wrap: wrap; }

.flex-item-grow { flex-grow: 1; }
.flex-item-shrink { flex-shrink: 1; }
.flex-item-no-shrink { flex-shrink: 0; }
.flex-item-justify-start { margin-inline-end: auto; }
.flex-item-justify-end { margin-inline-start: auto; }

.gap {
  column-gap: var(--column-gap, var(--inline-space));
  row-gap: var(--row-gap, var(--block-space));
}

.gap-half {
  column-gap: var(--inline-space-half);
  row-gap: var(--block-space-half);
}

/* Sizing */
.full-width { inline-size: 100%; }
.min-width { min-inline-size: 0; }
.half-width { inline-size: 50%; }
.max-width { max-inline-size: 100%; }
.min-content { inline-size: min-content; }
.max-inline-size { max-inline-size: 100%; }

/* Overflow */
.overflow-x { overflow-x: auto; scroll-snap-type: x mandatory; scroll-behavior: smooth; }
.overflow-y { overflow-y: auto; scroll-snap-type: y mandatory; scroll-behavior: smooth; }
.overflow-clip { text-overflow: clip; white-space: nowrap; overflow: hidden; }
.overflow-ellipsis { text-overflow: ellipsis; white-space: nowrap; overflow: hidden; }

.overflow-hide-scrollbar::-webkit-scrollbar {
  @media (pointer: course) {
    display: none;
  }
}

.overflow-line-clamp {
  -webkit-line-clamp: var(--lines, 2);
  -webkit-box-orient: vertical;
  display: -webkit-box;
  overflow: hidden;
  text-overflow: clip;
  white-space: normal;
}

/* Padding */
.pad { padding: var(--block-space) var(--inline-space); }
.pad-double { padding: var(--block-space-double) var(--inline-space-double); }

.pad-block { padding-block: var(--block-space); }
.pad-block-start { padding-block-start: var(--block-space); }
.pad-block-end { padding-block-end: var(--block-space); }
.pad-block-half { padding-block: var(--block-space-half); }
.pad-block-start-half { padding-block-start: var(--block-space-half); }

.pad-inline { padding-inline: var(--inline-space); }
.pad-inline-start { padding-inline-start: var(--inline-space); }
.pad-inline-end { padding-inline-end: var(--inline-space); }
.pad-inline-half { padding-inline: var(--inline-space-half); }
.pad-inline-double { padding-inline: var(--inline-space-double); }

.unpad { padding: 0; }

/* Margins */
.margin { margin: var(--block-space) var(--inline-space); }
.margin-block { margin-block: var(--block-space); }
.margin-block-half { margin-block: var(--block-space-half); }
.margin-block-start { margin-block-start: var(--block-space); }
.margin-block-start-half { margin-block-start: var(--block-space-half); }
.margin-block-end { margin-block-end: var(--block-space); }
.margin-block-end-half { margin-block-end: var(--block-space-half); }
.margin-block-double { margin-block: var(--block-space-double); }
.margin-block-end-double { margin-block-end: var(--block-space-double); }
.margin-block-start-double { margin-block-start: var(--block-space-double); }

.margin-inline { margin-inline: var(--inline-space); }
.margin-inline-start { margin-inline-start: var(--inline-space); }
.margin-inline-start-half { margin-inline-start: var(--inline-space-half); }
.margin-inline-end { margin-inline-end: var(--inline-space); }
.margin-inline-end-half { margin-inline-end: var(--inline-space-half); }
.margin-inline-half { margin-inline: var(--inline-space-half); }
.margin-inline-double { margin-inline: var(--inline-space-double); }

.margin-none { margin: 0; }
.margin-none-block { margin-block: 0; }
.margin-none-block-start { margin-block-start: 0; }
.margin-none-block-end { margin-block-end: 0; }

.margin-none-inline { margin-inline: 0; }
.margin-none-inline-start { margin-inline-start: 0; }
.margin-none-inline-end { margin-inline-end: 0; }

.center { margin-inline: auto; }
.center-block { margin-block: auto; }

/* Position */
.position-relative { position: relative; }
.position-sticky { position: sticky; }

/* Fills */
.fill { background-color: var(--color-bg); }
.fill-black { background-color: var(--color-ink); }
.fill-white { background-color: var(--color-ink-reversed); }
.fill-shade { background-color: var(--color-subtle-light); }
.fill-selected { background-color: var(--color-selected); }
.fill-transparent { background-color: transparent; }

.translucent { opacity: var(--opacity, 0.5); }

/* Borders */
.border { border: var(--border-size, 1px) solid var(--border-color, var(--color-subtle)); }
.border-top { border-top: var(--border-size, 1px) solid var(--border-color, var(--color-subtle)); }
.borderless { border: 0; }

/* Border radius */
.border-radius { border-radius: var(--border-radius, 1em); }

/* Shadows */
.shadow {
  box-shadow:
    0 0 0 1px oklch(var(--lch-always-black) / 0.02),
    0 .2em 1.6em -0.8em oklch(var(--lch-always-black) / 0.2),
    0 .4em 2.4em -1em oklch(var(--lch-always-black) / 0.3),
    0 .4em .8em -1.2em oklch(var(--lch-always-black) / 0.4),
    0 .8em 1.2em -1.6em oklch(var(--lch-always-black) / 0.5),
    0 1.2em 1.6em -2em oklch(var(--lch-always-black) / 0.6);

  @media (prefers-color-scheme: dark) {
    box-shadow:
      0 0 0 1px oklch(var(--lch-always-black) / 0.42),
      0 .2em 1.6em -0.8em oklch(var(--lch-always-black) / 0.6),
      0 .4em 2.4em -1em oklch(var(--lch-always-black) / 0.7),
      0 .4em .8em -1.2em oklch(var(--lch-always-black) / 0.8),
      0 .8em 1.2em -1.6em oklch(var(--lch-always-black) / 0.9),
      0 1.2em 1.6em -2em oklch(var(--lch-always-black) / 1);
  }
}

/* Seperators */
.separator {
  border-inline-start: 1px solid var(--color-subtle-dark);
  display: inline-flex;
  inline-size: 0;
}

/* Spinners */
.spinner {
  position: relative;

  &::before {
    --mask: no-repeat radial-gradient(#000 68%, #0000 71%);
    --dot-size: 1.25em;

    -webkit-mask: var(--mask), var(--mask), var(--mask);
    -webkit-mask-size: 28% 45%;
    animation: submitting 1.3s infinite linear;
    aspect-ratio: 8/5;
    background: currentColor;
    content: "";
    inline-size: var(--dot-size);
    inset: 50% 0.25em;
    margin-block: calc((var(--dot-size) / 3) * -1);
    margin-inline: calc((var(--dot-size) / 2) * -1);
    position: absolute;
  }
}

/* Accessibility */
.for-screen-reader {
  block-size: 1px;
  clip-path: inset(50%);
  inline-size: 1px;
  overflow: hidden;
  position: absolute;
  white-space: nowrap;
}

/* Visibility */
[hidden] { display: none; }
[contents] { display: contents; }
